#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于优化DBSCAN的完整系统重新执行
应用监督学习优化后的算法重新识别运行模式和检测异常
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import RobustScaler
import warnings
warnings.filterwarnings('ignore')
sys.path.append('.')

from 城市污染源异常检测系统 import CityAnomalyDetectionSystem

class OptimizedDBSCANSystem:
    """优化的DBSCAN完整系统"""
    
    def __init__(self):
        # 基于监督学习优化的参数
        self.optimized_config = {
            'shutdown_detection': {
                'zero_ratio_threshold': 0.15,  # 优化后的停产阈值
                'min_continuous_zeros': 20,
                'mixed_mode_threshold': 0.05
            },
            'pattern_params': {
                '单状态稳定运行': {
                    'dbscan': {'eps': 0.25, 'min_samples': 8},
                    'features': {'cv_max': 0.15, 'peaks_max': 2}
                },
                '双状态稳定运行': {
                    'dbscan': {'eps': 0.35, 'min_samples': 5},
                    'features': {'cv_min': 0.4, 'bimodality_min': 0.7}
                },
                '多状态稳定运行': {
                    'dbscan': {'eps': 0.40, 'min_samples': 8},
                    'features': {'cv_min': 0.8, 'peaks_min': 3}
                },
                '正常波动运行': {
                    'dbscan': {'eps': 0.20, 'min_samples': 3},
                    'features': {'cv_max': 0.3}
                }
            }
        }
        
        # 站点信息缓存
        self.site_info_cache = {}
    
    def execute_complete_optimization(self, system):
        """执行完整的优化流程"""
        
        print("🚀 基于优化DBSCAN的完整系统重新执行")
        print("="*80)
        
        # 第一步：重新识别运行模式
        print("\n📊 第一步：重新识别运行模式")
        optimized_patterns = self.reidentify_patterns(system)
        
        # 第二步：重新执行异常检测
        print("\n🚨 第二步：重新执行异常检测")
        optimized_anomalies = self.redetect_anomalies(system, optimized_patterns)
        
        # 第三步：重新生成散点图
        print("\n🎨 第三步：重新生成散点图")
        chart_results = self.regenerate_charts(system, optimized_patterns, optimized_anomalies)
        
        # 第四步：生成统计报告
        print("\n📋 第四步：生成统计报告")
        self.generate_comprehensive_report(optimized_patterns, optimized_anomalies, chart_results)
        
        return {
            'patterns': optimized_patterns,
            'anomalies': optimized_anomalies,
            'charts': chart_results
        }
    
    def reidentify_patterns(self, system):
        """重新识别运行模式"""
        
        optimized_patterns = {}
        total_sites = 0
        
        for month in system.monthly_data.keys():
            print(f"\n处理{month}月数据...")
            month_data = system.monthly_data[month]
            month_patterns = {}
            
            site_groups = month_data.groupby('site_id')
            month_sites = len(site_groups)
            total_sites += month_sites
            
            for i, (site_id, site_data) in enumerate(site_groups, 1):
                if i % 20 == 0 or i == month_sites:
                    print(f"  进度: {i}/{month_sites} 站点")
                
                flow_values = site_data['flow_value'].values
                
                # 使用优化算法识别模式
                pattern_result = self.classify_pattern_optimized(flow_values, site_id)
                month_patterns[site_id] = pattern_result
            
            optimized_patterns[month] = month_patterns
            print(f"  完成{month_sites}个站点的模式识别")
        
        # 统计模式分布
        self.print_pattern_statistics(optimized_patterns)
        
        return optimized_patterns
    
    def classify_pattern_optimized(self, flow_values, site_id):
        """使用优化算法分类模式"""
        
        if len(flow_values) < 20:
            return {
                'pattern_type': '数据不足',
                'confidence': 0.0,
                'features': {},
                'clustering_info': None
            }
        
        # 第一步：停产检测
        shutdown_result = self.detect_shutdown_optimized(flow_values)
        
        if shutdown_result['is_shutdown']:
            if shutdown_result['has_operation_periods']:
                return {
                    'pattern_type': '正常波动运行',  # 新规则：取消停产+正常波动
                    'confidence': 0.9,
                    'features': shutdown_result,
                    'clustering_info': None
                }
            else:
                return {
                    'pattern_type': '停产状态',
                    'confidence': 0.95,
                    'features': shutdown_result,
                    'clustering_info': None
                }
        
        # 第二步：提取运行数据
        operational_data = self.extract_operational_data(flow_values, shutdown_result)
        
        if len(operational_data) < 20:
            return {
                'pattern_type': '数据不足',
                'confidence': 0.0,
                'features': {},
                'clustering_info': None
            }
        
        # 第三步：特征提取
        features = self.extract_enhanced_features(operational_data)
        
        # 第四步：多层次识别
        pattern_type, confidence, clustering_info = self.multi_layer_recognition(operational_data, features)
        
        return {
            'pattern_type': pattern_type,
            'confidence': confidence,
            'features': features,
            'clustering_info': clustering_info
        }
    
    def detect_shutdown_optimized(self, flow_values):
        """优化的停产检测"""
        
        zero_ratio = (flow_values == 0).sum() / len(flow_values)
        
        # 使用优化的阈值
        threshold = self.optimized_config['shutdown_detection']['zero_ratio_threshold']
        
        is_shutdown = zero_ratio >= threshold
        has_operation_periods = False
        
        if is_shutdown:
            # 检查是否有显著运行期间
            non_zero_segments = self.analyze_non_zero_segments(flow_values)
            has_operation_periods = any(
                seg['length'] >= 50 and seg['mean_value'] > 100 
                for seg in non_zero_segments
            )
        
        return {
            'is_shutdown': is_shutdown,
            'has_operation_periods': has_operation_periods,
            'zero_ratio': zero_ratio,
            'non_zero_segments': non_zero_segments if is_shutdown else []
        }
    
    def analyze_non_zero_segments(self, flow_values):
        """分析非零值连续区间"""
        
        segments = []
        in_segment = False
        segment_start = 0
        segment_values = []
        
        for i, value in enumerate(flow_values):
            if value > 0:
                if not in_segment:
                    in_segment = True
                    segment_start = i
                    segment_values = [value]
                else:
                    segment_values.append(value)
            else:
                if in_segment:
                    segments.append({
                        'start': segment_start,
                        'end': i - 1,
                        'length': i - segment_start,
                        'mean_value': np.mean(segment_values),
                        'std_value': np.std(segment_values)
                    })
                    in_segment = False
        
        if in_segment:
            segments.append({
                'start': segment_start,
                'end': len(flow_values) - 1,
                'length': len(flow_values) - segment_start,
                'mean_value': np.mean(segment_values),
                'std_value': np.std(segment_values)
            })
        
        return segments
    
    def extract_operational_data(self, flow_values, shutdown_result):
        """提取运行数据"""
        
        if not shutdown_result['is_shutdown']:
            return flow_values[flow_values > 0]
        
        # 对于混合模式，保留运行期间的数据
        operational_indices = []
        for segment in shutdown_result['non_zero_segments']:
            if segment['length'] >= 10:  # 保留较长的运行区间
                for i in range(segment['start'], segment['end'] + 1):
                    if i < len(flow_values) and flow_values[i] > 0:
                        operational_indices.append(i)
        
        if operational_indices:
            return flow_values[operational_indices]
        else:
            return flow_values[flow_values > 0]
    
    def extract_enhanced_features(self, data):
        """提取增强特征"""
        
        data_series = pd.Series(data)
        
        features = {
            'count': len(data),
            'mean': data_series.mean(),
            'std': data_series.std(),
            'cv': data_series.std() / data_series.mean() if data_series.mean() > 0 else 0,
            'skewness': data_series.skew(),
            'kurtosis': data_series.kurtosis(),
            'q25': data_series.quantile(0.25),
            'q75': data_series.quantile(0.75),
            'iqr': data_series.quantile(0.75) - data_series.quantile(0.25),
            'density_peaks': self.count_density_peaks(data_series),
            'bimodality_coefficient': self.calculate_bimodality_coefficient(data_series),
            'gap_ratio': self.calculate_gap_ratio(data_series)
        }
        
        return features
    
    def count_density_peaks(self, data, bins=None):
        """计算密度峰数量"""
        try:
            if bins is None:
                bins = min(15, max(5, len(data) // 15))
            
            hist, bin_edges = np.histogram(data, bins=bins)
            
            if len(hist) >= 3:
                smoothed_hist = np.convolve(hist, [0.25, 0.5, 0.25], mode='same')
            else:
                smoothed_hist = hist
            
            peaks = 0
            threshold = np.mean(smoothed_hist) * 0.6
            
            for i in range(1, len(smoothed_hist)-1):
                if (smoothed_hist[i] > smoothed_hist[i-1] and 
                    smoothed_hist[i] > smoothed_hist[i+1] and 
                    smoothed_hist[i] > threshold):
                    peaks += 1
            
            return peaks
        except:
            return 1
    
    def calculate_bimodality_coefficient(self, data):
        """计算双峰系数"""
        try:
            skew = data.skew()
            kurt = data.kurtosis()
            n = len(data)
            
            if n > 3:
                bc = (skew**2 + 1) / (kurt + 3 * (n-1)**2 / ((n-2)*(n-3)))
                return bc
            return 0
        except:
            return 0
    
    def calculate_gap_ratio(self, data):
        """计算间隙比例"""
        try:
            sorted_data = np.sort(data)
            gaps = np.diff(sorted_data)
            if len(gaps) > 0:
                mean_gap = np.mean(gaps)
                max_gap = np.max(gaps)
                return max_gap / mean_gap if mean_gap > 0 else 0
            return 0
        except:
            return 0
    
    def multi_layer_recognition(self, data, features):
        """多层次识别"""
        
        # 计算各模式的得分
        pattern_scores = {}
        clustering_results = {}
        
        for pattern_type, config in self.optimized_config['pattern_params'].items():
            # 特征匹配得分
            feature_score = self.calculate_feature_score(features, config['features'])
            
            # DBSCAN聚类得分
            cluster_score, cluster_info = self.evaluate_dbscan_for_pattern(data, config['dbscan'], pattern_type)
            
            # 综合得分
            total_score = feature_score * 0.6 + cluster_score * 0.4
            
            pattern_scores[pattern_type] = total_score
            clustering_results[pattern_type] = cluster_info
        
        # 选择最佳模式
        best_pattern = max(pattern_scores, key=pattern_scores.get)
        best_score = pattern_scores[best_pattern]
        
        # 计算置信度
        scores_list = sorted(pattern_scores.values(), reverse=True)
        confidence = (scores_list[0] - scores_list[1]) / scores_list[0] if len(scores_list) >= 2 and scores_list[0] > 0 else 0.5
        
        # 应用特殊规则
        best_pattern = self.apply_special_rules(best_pattern, features, clustering_results)
        
        return best_pattern, confidence, clustering_results[best_pattern]
    
    def calculate_feature_score(self, features, feature_config):
        """计算特征匹配得分"""
        
        score = 0
        
        # CV检查
        if 'cv_max' in feature_config and features['cv'] <= feature_config['cv_max']:
            score += 10
        elif 'cv_min' in feature_config and features['cv'] >= feature_config['cv_min']:
            score += 10
        
        # 峰数检查
        if 'peaks_max' in feature_config and features['density_peaks'] <= feature_config['peaks_max']:
            score += 8
        elif 'peaks_min' in feature_config and features['density_peaks'] >= feature_config['peaks_min']:
            score += 8
        
        # 双峰系数检查
        if 'bimodality_min' in feature_config and features['bimodality_coefficient'] >= feature_config['bimodality_min']:
            score += 5
        
        return score
    
    def evaluate_dbscan_for_pattern(self, data, dbscan_config, pattern_type):
        """评估DBSCAN对特定模式的适合度"""
        
        try:
            scaler = RobustScaler()
            data_scaled = scaler.fit_transform(data.reshape(-1, 1))
            
            dbscan = DBSCAN(eps=dbscan_config['eps'], min_samples=dbscan_config['min_samples'])
            labels = dbscan.fit_predict(data_scaled)
            
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            noise_ratio = (labels == -1).sum() / len(labels)
            
            # 计算得分
            score = 0
            
            # 基于期望聚类数评分
            if pattern_type == '单状态稳定运行' and n_clusters == 1:
                score += 20
            elif pattern_type == '双状态稳定运行' and n_clusters == 2:
                score += 20
            elif pattern_type == '多状态稳定运行' and n_clusters >= 3:
                score += 20
            elif pattern_type == '正常波动运行' and n_clusters <= 2:
                score += 15
            else:
                score -= abs(n_clusters - 1) * 3
            
            # 噪声比例评分
            if noise_ratio < 0.1:
                score += 10
            elif noise_ratio < 0.2:
                score += 5
            else:
                score -= noise_ratio * 20
            
            cluster_info = {
                'n_clusters': n_clusters,
                'noise_ratio': noise_ratio,
                'labels': labels,
                'eps': dbscan_config['eps'],
                'min_samples': dbscan_config['min_samples']
            }
            
            return max(0, score), cluster_info
            
        except Exception as e:
            return 0, {'error': str(e), 'n_clusters': 0, 'noise_ratio': 1.0}
    
    def apply_special_rules(self, predicted_pattern, features, clustering_results):
        """应用特殊规则"""
        
        # 特殊规则1：高CV但单聚类
        if (features['cv'] > 0.15 and 
            predicted_pattern == '单状态稳定运行' and
            clustering_results.get('单状态稳定运行', {}).get('n_clusters', 0) == 1):
            
            if features['bimodality_coefficient'] > 0.6:
                return '双状态稳定运行'
        
        # 特殊规则2：低峰数但高CV
        if (features['density_peaks'] < 2 and 
            features['cv'] > 0.4 and
            predicted_pattern == '正常波动运行'):
            
            # 检查双状态聚类结果
            dual_result = clustering_results.get('双状态稳定运行', {})
            if dual_result.get('n_clusters', 0) == 2:
                return '双状态稳定运行'
        
        return predicted_pattern
    
    def print_pattern_statistics(self, optimized_patterns):
        """打印模式统计"""
        
        pattern_stats = {}
        total_sites = 0
        
        for month, patterns in optimized_patterns.items():
            for site_id, pattern_info in patterns.items():
                pattern_type = pattern_info['pattern_type']
                pattern_stats[pattern_type] = pattern_stats.get(pattern_type, 0) + 1
                total_sites += 1
        
        print(f"\n📊 优化后运行模式分布 (总计{total_sites}个站点月度组合):")
        for pattern, count in sorted(pattern_stats.items()):
            percentage = count / total_sites * 100
            print(f"   {pattern}: {count}个 ({percentage:.1f}%)")

    def redetect_anomalies(self, system, optimized_patterns):
        """重新执行异常检测"""

        optimized_anomalies = {}
        total_anomalies = 0

        for month in system.monthly_data.keys():
            print(f"\n处理{month}月异常检测...")
            month_data = system.monthly_data[month]
            month_anomalies = {}

            site_groups = month_data.groupby('site_id')
            month_sites = len(site_groups)

            for i, (site_id, site_data) in enumerate(site_groups, 1):
                if i % 20 == 0 or i == month_sites:
                    print(f"  进度: {i}/{month_sites} 站点")

                if site_id in optimized_patterns[month]:
                    pattern_info = optimized_patterns[month][site_id]

                    # 基于优化模式执行异常检测
                    anomalies = self.detect_anomalies_optimized(site_data, pattern_info)

                    month_anomalies[site_id] = {
                        'pattern_type': pattern_info['pattern_type'],
                        'anomalies': anomalies,
                        'anomaly_count': len(anomalies)
                    }

                    total_anomalies += len(anomalies)

            optimized_anomalies[month] = month_anomalies
            month_total = sum(result['anomaly_count'] for result in month_anomalies.values())
            print(f"  检测到{month_total}个异常值")

        print(f"\n📊 总计检测到{total_anomalies}个异常值")
        return optimized_anomalies

    def detect_anomalies_optimized(self, site_data, pattern_info):
        """基于优化模式的异常检测"""

        flow_data = site_data['flow_value']
        anomalies = []

        # 1. 负值异常检测
        negative_indices = site_data[site_data['flow_value'] < 0].index
        for idx in negative_indices:
            anomalies.append({
                'index': idx,
                'timestamp': site_data.loc[idx, 'timestamp'],
                'value': site_data.loc[idx, 'flow_value'],
                'anomaly_type': '负值异常',
                'detection_method': '业务规则检测'
            })

        # 2. 基于运行模式的异常检测
        pattern_type = pattern_info['pattern_type']
        positive_data = flow_data[flow_data > 0]

        if len(positive_data) >= 5:
            if pattern_type in ['单状态稳定运行', '正常波动运行']:
                # 使用统计学方法
                anomalies.extend(self.detect_statistical_anomalies(site_data, positive_data))

            elif pattern_type in ['双状态稳定运行', '多状态稳定运行']:
                # 使用分状态统计学方法
                anomalies.extend(self.detect_state_based_anomalies(site_data, positive_data, pattern_info))

        return anomalies

    def detect_statistical_anomalies(self, site_data, positive_data):
        """统计学异常检测"""

        anomalies = []

        if len(positive_data) < 5:
            return anomalies

        # P5/P95方法
        p5 = positive_data.quantile(0.05)
        p95 = positive_data.quantile(0.95)

        # IQR方法
        q1 = positive_data.quantile(0.25)
        q3 = positive_data.quantile(0.75)
        iqr = q3 - q1
        iqr_lower = q1 - 1.5 * iqr
        iqr_upper = q3 + 1.5 * iqr

        # MAD方法
        median = positive_data.median()
        mad = np.median(np.abs(positive_data - median))
        mad_lower = median - 3 * mad
        mad_upper = median + 3 * mad

        # 检测异常
        positive_indices = site_data[site_data['flow_value'] > 0].index

        for idx in positive_indices:
            value = site_data.loc[idx, 'flow_value']

            methods = []
            if value < p5 or value > p95:
                methods.append('P5/P95')
            if value < iqr_lower or value > iqr_upper:
                methods.append('IQR')
            if mad > 0 and (value < mad_lower or value > mad_upper):
                methods.append('MAD')

            if methods:
                anomaly_type = '明显异常' if len(methods) >= 2 else '统计异常'

                anomalies.append({
                    'index': idx,
                    'timestamp': site_data.loc[idx, 'timestamp'],
                    'value': value,
                    'anomaly_type': anomaly_type,
                    'detection_method': '+'.join(methods)
                })

        return anomalies

    def detect_state_based_anomalies(self, site_data, positive_data, pattern_info):
        """分状态异常检测"""

        anomalies = []

        # 获取聚类信息
        clustering_info = pattern_info.get('clustering_info')
        if not clustering_info or 'labels' not in clustering_info:
            # 如果没有聚类信息，使用全局统计方法
            return self.detect_statistical_anomalies(site_data, positive_data)

        labels = clustering_info['labels']
        unique_states = sorted(set(labels) - {-1})

        # 在每个状态内使用统计学方法
        for state_id in unique_states:
            state_mask = labels == state_id
            state_data = positive_data[state_mask]
            state_indices = positive_data.index[state_mask]

            if len(state_data) >= 3:
                # 使用更严格的统计方法
                p10 = state_data.quantile(0.10)
                p90 = state_data.quantile(0.90)

                q1 = state_data.quantile(0.25)
                q3 = state_data.quantile(0.75)
                iqr = q3 - q1
                iqr_lower = q1 - 1.5 * iqr
                iqr_upper = q3 + 1.5 * iqr

                for idx in state_indices:
                    if idx in site_data.index:
                        value = site_data.loc[idx, 'flow_value']

                        methods = []
                        if value < p10 or value > p90:
                            methods.append('P10/P90')
                        if iqr > 0 and (value < iqr_lower or value > iqr_upper):
                            methods.append('IQR')

                        if methods:
                            anomalies.append({
                                'index': idx,
                                'timestamp': site_data.loc[idx, 'timestamp'],
                                'value': value,
                                'anomaly_type': '状态内异常',
                                'detection_method': f'状态{state_id}内{"+".join(methods)}'
                            })

        return anomalies

    def regenerate_charts(self, system, optimized_patterns, optimized_anomalies):
        """重新生成散点图"""

        # 确保输出目录存在
        chart_dir = os.path.join("检测报告", "站点月度图表")
        os.makedirs(chart_dir, exist_ok=True)

        chart_results = {}
        total_charts = 0

        for month in system.monthly_data.keys():
            print(f"\n生成{month}月图表...")
            month_data = system.monthly_data[month]
            month_charts = {}

            site_groups = month_data.groupby('site_id')
            month_sites = len(site_groups)

            for i, (site_id, site_data) in enumerate(site_groups, 1):
                if i % 20 == 0 or i == month_sites:
                    print(f"  进度: {i}/{month_sites} 站点")

                if (site_id in optimized_patterns[month] and
                    site_id in optimized_anomalies[month]):

                    pattern_info = optimized_patterns[month][site_id]
                    anomaly_info = optimized_anomalies[month][site_id]

                    # 获取站点信息
                    company_name, site_name = self.get_site_info(system, site_id)

                    # 生成图表
                    chart_success = self.create_optimized_chart(
                        site_data, site_id, month, company_name, site_name,
                        pattern_info, anomaly_info, chart_dir
                    )

                    if chart_success:
                        month_charts[site_id] = {
                            'pattern_type': pattern_info['pattern_type'],
                            'anomaly_count': anomaly_info['anomaly_count'],
                            'chart_generated': True
                        }
                        total_charts += 1

            chart_results[month] = month_charts
            print(f"  生成{len(month_charts)}个图表")

        print(f"\n✅ 总共生成{total_charts}个优化图表")
        return chart_results

    def get_site_info(self, system, site_id):
        """获取站点信息，解决中文显示问题"""

        if site_id in self.site_info_cache:
            return self.site_info_cache[site_id]

        company_name = "未知企业"
        site_name = "未知站点"

        # 从系统中获取站点信息
        if hasattr(system, 'site_profiles') and site_id in system.site_profiles:
            profile = system.site_profiles[site_id]
            company_name = profile.get('company_name', '未知企业')
            site_name = profile.get('site_name', '未知站点')
        else:
            # 从数据中推断站点信息
            for month_data in system.monthly_data.values():
                site_records = month_data[month_data['site_id'] == site_id]
                if len(site_records) > 0:
                    first_record = site_records.iloc[0]
                    if 'company_name' in first_record:
                        company_name = str(first_record['company_name'])
                    if 'site_name' in first_record:
                        site_name = str(first_record['site_name'])
                    break

        # 处理中文编码问题
        try:
            if isinstance(company_name, bytes):
                company_name = company_name.decode('utf-8', errors='ignore')
            if isinstance(site_name, bytes):
                site_name = site_name.decode('utf-8', errors='ignore')

            # 清理特殊字符
            company_name = str(company_name).strip()
            site_name = str(site_name).strip()

            # 如果仍然是乱码或空值，使用默认值
            if not company_name or len(company_name) < 2:
                company_name = "未知企业"
            if not site_name or len(site_name) < 2:
                site_name = "未知站点"

        except Exception as e:
            company_name = "未知企业"
            site_name = "未知站点"

        # 缓存结果
        self.site_info_cache[site_id] = (company_name, site_name)

        return company_name, site_name

    def create_optimized_chart(self, site_data, site_id, month, company_name, site_name, pattern_info, anomaly_info, chart_dir):
        """创建优化的散点图"""

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        plt.figure(figsize=(14, 8))

        # 按时间戳排序数据
        site_month_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)

        # 分离不同类型的数据
        zero_data = site_month_data_sorted[site_month_data_sorted['flow_value'] == 0]
        positive_data = site_month_data_sorted[site_month_data_sorted['flow_value'] > 0]
        negative_data = site_month_data_sorted[site_month_data_sorted['flow_value'] < 0]

        # 1. 绘制零值（绿色圆点）
        if len(zero_data) > 0:
            plt.scatter(zero_data['timestamp'], zero_data['flow_value'],
                       c='green', alpha=0.7, s=25, label='零值',
                       marker='o', edgecolors='darkgreen', linewidth=0.5)

        # 2. 绘制正常值（蓝色）
        if len(positive_data) > 0:
            plt.scatter(positive_data['timestamp'], positive_data['flow_value'],
                       c='blue', alpha=0.6, s=20, label='正常值')

        # 3. 绘制负值异常（黑色叉号）
        if len(negative_data) > 0:
            plt.scatter(negative_data['timestamp'], negative_data['flow_value'],
                       c='black', alpha=0.9, s=30,
                       label='负值异常', marker='x', linewidth=1.5)

        # 4. 标记异常值
        anomalies = anomaly_info['anomalies']
        if anomalies:
            # 分类异常值
            light_anomaly_times = []
            light_anomaly_values = []
            moderate_anomaly_times = []
            moderate_anomaly_values = []
            significant_anomaly_times = []
            significant_anomaly_values = []

            for anomaly in anomalies:
                if anomaly['value'] > 0:  # 只处理正值异常
                    timestamp = anomaly['timestamp']
                    value = anomaly['value']
                    anomaly_type = anomaly['anomaly_type']

                    if anomaly_type == '明显异常':
                        significant_anomaly_times.append(timestamp)
                        significant_anomaly_values.append(value)
                    elif anomaly_type in ['状态内异常', '统计异常']:
                        moderate_anomaly_times.append(timestamp)
                        moderate_anomaly_values.append(value)
                    else:
                        light_anomaly_times.append(timestamp)
                        light_anomaly_values.append(value)

            # 绘制轻度异常（空心圆圈）
            if light_anomaly_times:
                plt.scatter(light_anomaly_times, light_anomaly_values,
                           c='none', alpha=0.8, s=35, label='统计异常(轻度)',
                           marker='o', edgecolors='orange', linewidth=1.2)

            # 绘制中度异常（填充圆圈）
            if moderate_anomaly_times:
                plt.scatter(moderate_anomaly_times, moderate_anomaly_values,
                           c='yellow', alpha=0.6, s=40, label='统计异常(中度)',
                           marker='o', edgecolors='orange', linewidth=1.5)

            # 绘制明显异常（红色圆圈）
            if significant_anomaly_times:
                plt.scatter(significant_anomaly_times, significant_anomaly_values,
                           c='red', alpha=0.9, s=50, label='明显异常值',
                           marker='o', edgecolors='darkred', linewidth=1)

        # 设置图表属性
        pattern_type = pattern_info['pattern_type']
        confidence = pattern_info.get('confidence', 0)

        # 修复标题显示问题
        title = f'{company_name}-{site_name}-{month}月\n运行模式: {pattern_type} (置信度: {confidence:.2f})'
        plt.title(title, fontsize=14, fontweight='bold')

        plt.xlabel('时间戳（时间序列）', fontsize=12)
        plt.ylabel('流量值', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)

        # 设置时间轴格式
        if len(site_month_data_sorted) > 0:
            plt.gca().xaxis.set_major_formatter(DateFormatter('%m-%d %H:%M'))
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(site_month_data_sorted)//10)))
            plt.xticks(rotation=45)

            # 显示时间范围
            start_time = site_month_data_sorted['timestamp'].min()
            end_time = site_month_data_sorted['timestamp'].max()
            plt.figtext(0.02, 0.02, f'时间范围: {start_time.strftime("%Y-%m-%d %H:%M")} 至 {end_time.strftime("%Y-%m-%d %H:%M")}',
                       fontsize=8, alpha=0.7)

        # 保存图表
        safe_site_id = str(site_id).replace('/', '_').replace('\\\\', '_')[:20]
        chart_filename = f"{safe_site_id}_{month}月.png"
        chart_path = os.path.join(chart_dir, chart_filename)

        plt.tight_layout()
        plt.savefig(chart_path, dpi=150, bbox_inches='tight')
        plt.close()

        # 验证文件生成
        if os.path.exists(chart_path):
            return True
        else:
            return False

    def generate_comprehensive_report(self, optimized_patterns, optimized_anomalies, chart_results):
        """生成综合报告"""

        report_path = os.path.join("检测报告", f"优化DBSCAN系统执行报告_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("基于优化DBSCAN的完整系统重新执行报告\n")
            f.write("="*80 + "\n\n")

            f.write(f"执行时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"优化方法: 基于监督学习的DBSCAN优化\n\n")

            # 统计模式分布
            pattern_stats = {}
            total_sites = 0
            for month, patterns in optimized_patterns.items():
                for site_id, pattern_info in patterns.items():
                    pattern_type = pattern_info['pattern_type']
                    pattern_stats[pattern_type] = pattern_stats.get(pattern_type, 0) + 1
                    total_sites += 1

            f.write("1. 运行模式识别结果\n")
            f.write("-"*40 + "\n")
            f.write(f"总处理站点月度组合: {total_sites}个\n\n")
            for pattern, count in sorted(pattern_stats.items()):
                percentage = count / total_sites * 100
                f.write(f"{pattern}: {count}个 ({percentage:.1f}%)\n")

            # 统计异常检测结果
            total_anomalies = 0
            anomaly_by_pattern = {}
            for month, anomalies in optimized_anomalies.items():
                for site_id, anomaly_info in anomalies.items():
                    pattern_type = anomaly_info['pattern_type']
                    anomaly_count = anomaly_info['anomaly_count']
                    total_anomalies += anomaly_count
                    anomaly_by_pattern[pattern_type] = anomaly_by_pattern.get(pattern_type, 0) + anomaly_count

            f.write(f"\n2. 异常检测结果\n")
            f.write("-"*40 + "\n")
            f.write(f"总异常数: {total_anomalies}个\n\n")
            for pattern, count in sorted(anomaly_by_pattern.items()):
                f.write(f"{pattern}: {count}个异常\n")

            # 统计图表生成结果
            total_charts = sum(len(month_charts) for month_charts in chart_results.values())
            f.write(f"\n3. 图表生成结果\n")
            f.write("-"*40 + "\n")
            f.write(f"生成图表总数: {total_charts}个\n")
            f.write("按月份分布:\n")
            for month in sorted(chart_results.keys()):
                month_count = len(chart_results[month])
                f.write(f"  {month}月: {month_count}个图表\n")

            f.write(f"\n4. 技术参数\n")
            f.write("-"*40 + "\n")
            f.write("优化的DBSCAN参数:\n")
            f.write("  单状态稳定运行: eps=0.25, min_samples=8\n")
            f.write("  双状态稳定运行: eps=0.35, min_samples=5\n")
            f.write("  多状态稳定运行: eps=0.40, min_samples=8\n")
            f.write("  正常波动运行: eps=0.20, min_samples=3\n")
            f.write("停产检测阈值: 零值比例≥0.15\n")
            f.write("图表格式: 14x8, DPI=150, 五色标记系统\n")
            f.write("输出位置: 检测报告/站点月度图表/\n")

        print(f"✅ 综合报告已生成: {os.path.basename(report_path)}")

def main():
    """主函数"""

    # 初始化系统
    system = CityAnomalyDetectionSystem("唐山")
    system.load_and_preprocess_data()

    # 创建优化系统
    optimizer = OptimizedDBSCANSystem()

    # 执行完整优化
    results = optimizer.execute_complete_optimization(system)

    print(f"\n🎉 优化系统重新执行完成！")

    # 统计结果
    pattern_count = sum(len(patterns) for patterns in results['patterns'].values())
    anomaly_count = sum(sum(anomaly_info['anomaly_count'] for anomaly_info in month_anomalies.values())
                       for month_anomalies in results['anomalies'].values())
    chart_count = sum(len(charts) for charts in results['charts'].values())

    print(f"\n📊 执行统计:")
    print(f"   处理站点月度组合: {pattern_count}个")
    print(f"   检测异常值: {anomaly_count}个")
    print(f"   生成图表: {chart_count}个")
    print(f"   输出位置: 检测报告/站点月度图表/")

if __name__ == "__main__":
    main()
