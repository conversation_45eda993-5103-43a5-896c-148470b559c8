#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DBSCAN专用模式识别优化框架
提高识别点位运行模式准确率的系统性方法
广泛适用于其他点位的模式识别
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.neighbors import NearestNeighbors
from scipy import stats
from scipy.spatial.distance import pdist, squareform
import warnings
warnings.filterwarnings('ignore')
sys.path.append('.')

class DBSCANPatternRecognitionOptimizer:
    """DBSCAN模式识别优化器"""
    
    def __init__(self):
        self.scaler_types = {
            'standard': StandardScaler(),
            'robust': RobustScaler(),
            'minmax': MinMaxScaler()
        }
        
        # 预定义的模式特征阈值
        self.pattern_thresholds = {
            'single_state': {'cv_max': 0.25, 'gap_ratio_max': 50, 'peaks_max': 2},
            'dual_state': {'cv_range': (0.3, 0.8), 'gap_ratio_min': 60, 'peaks_range': (2, 4)},
            'multi_state': {'cv_min': 0.6, 'gap_ratio_min': 80, 'peaks_min': 3},
            'normal_fluctuation': {'cv_max': 0.4, 'stability_min': 0.85},
            'shutdown_mixed': {'zero_ratio_min': 0.2, 'cv_range': (0.2, 1.0)}
        }
    
    def optimize_dbscan_parameters(self, data, expected_pattern=None):
        """优化DBSCAN参数的核心方法"""
        
        print(f"🔧 开始DBSCAN参数优化")
        print(f"数据点数: {len(data)}")
        
        # 1. 数据预处理和特征提取
        processed_data, features = self.preprocess_and_extract_features(data)
        
        # 2. 自适应参数范围确定
        param_ranges = self.determine_adaptive_parameter_ranges(processed_data, features, expected_pattern)
        
        # 3. 智能参数搜索
        best_params = self.intelligent_parameter_search(processed_data, param_ranges, features, expected_pattern)
        
        # 4. 结果验证和优化
        final_params = self.validate_and_refine_parameters(processed_data, best_params, features, expected_pattern)
        
        return final_params
    
    def preprocess_and_extract_features(self, data):
        """数据预处理和特征提取"""
        
        # 移除异常值
        cleaned_data = self.remove_outliers(data)
        
        # 提取全面特征
        features = self.extract_comprehensive_features(cleaned_data)
        
        # 选择最佳标准化方法
        best_scaler = self.select_best_scaler(cleaned_data, features)
        processed_data = best_scaler.fit_transform(cleaned_data.values.reshape(-1, 1))
        
        return processed_data, features
    
    def remove_outliers(self, data, method='iqr_robust'):
        """移除异常值"""
        
        if method == 'iqr_robust':
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            
            # 使用更宽松的异常值定义
            lower_bound = Q1 - 2.5 * IQR
            upper_bound = Q3 + 2.5 * IQR
            
            cleaned_data = data[(data >= lower_bound) & (data <= upper_bound)]
            
        elif method == 'zscore_modified':
            # 修正的Z-score方法
            median = data.median()
            mad = np.median(np.abs(data - median))
            modified_z_scores = 0.6745 * (data - median) / mad
            cleaned_data = data[np.abs(modified_z_scores) < 3.5]
        
        else:
            cleaned_data = data
        
        print(f"  异常值移除: {len(data)} → {len(cleaned_data)} 个点 (保留率: {len(cleaned_data)/len(data)*100:.1f}%)")
        
        return cleaned_data
    
    def extract_comprehensive_features(self, data):
        """提取全面的数据特征"""
        
        features = {}
        
        # 基础统计特征
        features['count'] = len(data)
        features['mean'] = data.mean()
        features['std'] = data.std()
        features['cv'] = data.std() / data.mean() if data.mean() > 0 else 0
        features['skewness'] = data.skew()
        features['kurtosis'] = data.kurtosis()
        
        # 分位数特征
        features['q10'] = data.quantile(0.10)
        features['q25'] = data.quantile(0.25)
        features['q50'] = data.quantile(0.50)
        features['q75'] = data.quantile(0.75)
        features['q90'] = data.quantile(0.90)
        features['iqr'] = features['q75'] - features['q25']
        features['iqr_ratio'] = features['iqr'] / features['mean'] if features['mean'] > 0 else 0
        
        # 分布形状特征
        features['range_ratio'] = (data.max() - data.min()) / data.mean() if data.mean() > 0 else 0
        features['gap_ratio'] = self.calculate_gap_ratio(data)
        features['density_peaks'] = self.count_density_peaks(data)
        features['bimodality_coefficient'] = self.calculate_bimodality_coefficient(data)
        
        # 聚类倾向特征
        features['hopkins_statistic'] = self.calculate_hopkins_statistic(data)
        features['nearest_neighbor_ratio'] = self.calculate_nearest_neighbor_ratio(data)
        
        # 稳定性特征
        features['stability_index'] = self.calculate_stability_index(data)
        features['transition_frequency'] = self.calculate_transition_frequency(data)
        
        return features
    
    def calculate_gap_ratio(self, data):
        """计算间隙比例"""
        try:
            sorted_data = np.sort(data)
            gaps = np.diff(sorted_data)
            if len(gaps) > 0:
                mean_gap = np.mean(gaps)
                max_gap = np.max(gaps)
                return max_gap / mean_gap if mean_gap > 0 else 0
            return 0
        except:
            return 0
    
    def count_density_peaks(self, data, bins=None):
        """计算密度峰数量"""
        try:
            if bins is None:
                bins = min(20, max(5, len(data) // 20))
            
            hist, bin_edges = np.histogram(data, bins=bins)
            
            # 平滑处理
            if len(hist) >= 3:
                smoothed_hist = np.convolve(hist, [0.25, 0.5, 0.25], mode='same')
            else:
                smoothed_hist = hist
            
            # 寻找局部最大值
            peaks = 0
            for i in range(1, len(smoothed_hist)-1):
                if smoothed_hist[i] > smoothed_hist[i-1] and smoothed_hist[i] > smoothed_hist[i+1]:
                    # 只计算显著的峰
                    if smoothed_hist[i] > np.mean(smoothed_hist) * 0.5:
                        peaks += 1
            
            return peaks
        except:
            return 1
    
    def calculate_bimodality_coefficient(self, data):
        """计算双峰系数"""
        try:
            skew = data.skew()
            kurt = data.kurtosis()
            n = len(data)
            
            if n > 3:
                bc = (skew**2 + 1) / (kurt + 3 * (n-1)**2 / ((n-2)*(n-3)))
                return bc
            return 0
        except:
            return 0
    
    def calculate_hopkins_statistic(self, data, sample_size=None):
        """计算Hopkins统计量"""
        try:
            if sample_size is None:
                sample_size = min(50, len(data) // 4)
            
            if sample_size < 5:
                return 0.5
            
            data_array = data.values.reshape(-1, 1)
            
            # 随机采样
            random_indices = np.random.choice(len(data_array), sample_size, replace=False)
            sample_data = data_array[random_indices]
            
            # 计算到最近邻的距离
            nbrs = NearestNeighbors(n_neighbors=2).fit(data_array)
            distances, _ = nbrs.kneighbors(sample_data)
            u_distances = distances[:, 1]
            
            # 生成随机点
            data_min, data_max = data_array.min(), data_array.max()
            random_points = np.random.uniform(data_min, data_max, (sample_size, 1))
            
            random_distances, _ = nbrs.kneighbors(random_points)
            w_distances = random_distances[:, 0]
            
            hopkins = np.sum(w_distances) / (np.sum(u_distances) + np.sum(w_distances))
            
            return hopkins
        except:
            return 0.5
    
    def calculate_nearest_neighbor_ratio(self, data):
        """计算最近邻比例"""
        try:
            if len(data) < 10:
                return 1.0
            
            data_array = data.values.reshape(-1, 1)
            nbrs = NearestNeighbors(n_neighbors=3).fit(data_array)
            distances, _ = nbrs.kneighbors(data_array)
            
            # 计算第一和第二最近邻距离的比例
            first_nn_dist = distances[:, 1]  # 第一最近邻
            second_nn_dist = distances[:, 2]  # 第二最近邻
            
            ratios = first_nn_dist / (second_nn_dist + 1e-10)
            return np.mean(ratios)
        except:
            return 1.0
    
    def calculate_stability_index(self, data):
        """计算稳定性指数"""
        try:
            n_segments = min(10, len(data) // 10)
            if n_segments < 2:
                return 1.0
            
            segment_size = len(data) // n_segments
            segment_means = []
            
            for i in range(n_segments):
                start_idx = i * segment_size
                end_idx = (i + 1) * segment_size if i < n_segments - 1 else len(data)
                segment_data = data.iloc[start_idx:end_idx]
                segment_means.append(segment_data.mean())
            
            segment_cv = np.std(segment_means) / np.mean(segment_means) if np.mean(segment_means) > 0 else 0
            return 1 / (1 + segment_cv)
        except:
            return 0.5
    
    def calculate_transition_frequency(self, data, threshold_ratio=0.1):
        """计算状态转换频率"""
        try:
            threshold = data.std() * threshold_ratio
            changes = np.abs(np.diff(data))
            significant_changes = (changes > threshold).sum()
            return significant_changes / len(data) if len(data) > 0 else 0
        except:
            return 0
    
    def select_best_scaler(self, data, features):
        """选择最佳标准化方法"""
        
        # 基于数据特征选择标准化方法
        if features['skewness'] > 2 or features['kurtosis'] > 5:
            # 数据偏态严重，使用鲁棒标准化
            return self.scaler_types['robust']
        elif features['cv'] > 1.0:
            # 变异系数大，使用MinMax标准化
            return self.scaler_types['minmax']
        else:
            # 默认使用标准标准化
            return self.scaler_types['standard']
    
    def determine_adaptive_parameter_ranges(self, processed_data, features, expected_pattern):
        """确定自适应参数范围"""
        
        data_size = len(processed_data)
        
        # 基于数据特征动态调整eps范围
        if features['cv'] < 0.2:
            # 低变异，较小eps
            eps_range = np.arange(0.1, 0.4, 0.05)
        elif features['cv'] > 0.8:
            # 高变异，较大eps
            eps_range = np.arange(0.2, 0.6, 0.05)
        else:
            # 中等变异，中等eps
            eps_range = np.arange(0.15, 0.5, 0.05)
        
        # 基于数据量动态调整min_samples范围
        if data_size < 100:
            min_samples_range = [3, 5, 8]
        elif data_size < 500:
            min_samples_range = [5, 8, 10, 15]
        else:
            min_samples_range = [8, 10, 15, 20, 25]
        
        # 基于期望模式进一步调整
        if expected_pattern:
            eps_range, min_samples_range = self.adjust_ranges_by_pattern(
                eps_range, min_samples_range, expected_pattern, features
            )
        
        return {
            'eps_range': eps_range,
            'min_samples_range': min_samples_range
        }
    
    def adjust_ranges_by_pattern(self, eps_range, min_samples_range, expected_pattern, features):
        """基于期望模式调整参数范围"""
        
        if expected_pattern == '单状态稳定运行':
            # 单状态需要较小eps，避免过度分割
            eps_range = eps_range[eps_range <= 0.35]
            
        elif expected_pattern == '双状态稳定运行':
            # 双状态需要适中eps，能够分离两个状态
            eps_range = eps_range[(eps_range >= 0.2) & (eps_range <= 0.45)]
            
        elif expected_pattern == '多状态稳定运行':
            # 多状态需要较大eps，处理多个聚类
            eps_range = eps_range[eps_range >= 0.25]
            
        elif expected_pattern == '正常波动':
            # 正常波动需要较小eps，保持聚类紧密
            eps_range = eps_range[eps_range <= 0.3]
            min_samples_range = [x for x in min_samples_range if x <= 10]
        
        return eps_range, min_samples_range
    
    def intelligent_parameter_search(self, processed_data, param_ranges, features, expected_pattern):
        """智能参数搜索"""
        
        best_score = -1
        best_params = None
        
        eps_range = param_ranges['eps_range']
        min_samples_range = param_ranges['min_samples_range']
        
        print(f"  参数搜索范围: eps={len(eps_range)}个值, min_samples={len(min_samples_range)}个值")
        
        for eps in eps_range:
            for min_samples in min_samples_range:
                if min_samples < len(processed_data) // 8:  # 确保参数合理
                    try:
                        # 执行DBSCAN聚类
                        dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                        labels = dbscan.fit_predict(processed_data)
                        
                        # 计算聚类质量得分
                        score = self.calculate_clustering_quality_score(
                            processed_data, labels, features, expected_pattern
                        )
                        
                        if score > best_score:
                            best_score = score
                            best_params = {
                                'eps': eps,
                                'min_samples': min_samples,
                                'labels': labels,
                                'score': score,
                                'n_clusters': len(set(labels)) - (1 if -1 in labels else 0),
                                'noise_ratio': (labels == -1).sum() / len(labels)
                            }
                    
                    except Exception as e:
                        continue
        
        if best_params:
            print(f"  最优参数: eps={best_params['eps']:.3f}, min_samples={best_params['min_samples']}")
            print(f"  聚类结果: {best_params['n_clusters']}个聚类, 噪声比例={best_params['noise_ratio']:.3f}")
            print(f"  质量得分: {best_params['score']:.2f}")
        
        return best_params
    
    def calculate_clustering_quality_score(self, processed_data, labels, features, expected_pattern):
        """计算聚类质量得分"""
        
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        noise_ratio = (labels == -1).sum() / len(labels)
        
        score = 0
        
        # 1. 基于期望模式的得分
        pattern_score = self.calculate_pattern_match_score(n_clusters, noise_ratio, expected_pattern)
        score += pattern_score * 0.4
        
        # 2. 聚类质量指标得分
        quality_score = self.calculate_intrinsic_quality_score(processed_data, labels, n_clusters)
        score += quality_score * 0.3
        
        # 3. 数据特征一致性得分
        consistency_score = self.calculate_feature_consistency_score(features, n_clusters, noise_ratio)
        score += consistency_score * 0.2
        
        # 4. 鲁棒性得分
        robustness_score = self.calculate_robustness_score(noise_ratio, n_clusters, len(processed_data))
        score += robustness_score * 0.1
        
        return score
    
    def calculate_pattern_match_score(self, n_clusters, noise_ratio, expected_pattern):
        """计算模式匹配得分"""
        
        score = 0
        
        if expected_pattern == '单状态稳定运行':
            if n_clusters == 1:
                score = 20
            elif n_clusters == 0:
                score = 10
            else:
                score = max(0, 20 - abs(n_clusters - 1) * 5)
        
        elif expected_pattern == '双状态稳定运行':
            if n_clusters == 2:
                score = 20
            elif n_clusters == 1:
                score = 8
            elif n_clusters == 3:
                score = 12
            else:
                score = max(0, 20 - abs(n_clusters - 2) * 4)
        
        elif expected_pattern == '多状态稳定运行':
            if n_clusters >= 3:
                score = 20
            elif n_clusters == 2:
                score = 10
            else:
                score = max(0, 20 - (3 - n_clusters) * 5)
        
        elif expected_pattern == '正常波动':
            if n_clusters <= 1:
                score = 18
            elif n_clusters == 2 and noise_ratio > 0.2:
                score = 12
            else:
                score = max(0, 18 - n_clusters * 3)
        
        elif expected_pattern == '停运+正常波动':
            if n_clusters <= 2:
                score = 18
            else:
                score = max(0, 18 - (n_clusters - 2) * 3)
        
        else:
            # 无期望模式时，偏好中等数量的聚类
            if 1 <= n_clusters <= 3:
                score = 15
            else:
                score = max(0, 15 - abs(n_clusters - 2) * 2)
        
        # 噪声比例调整
        if noise_ratio < 0.05:
            score += 5
        elif noise_ratio < 0.15:
            score += 3
        elif noise_ratio < 0.25:
            score += 1
        else:
            score -= (noise_ratio - 0.25) * 20
        
        return max(0, score)
    
    def calculate_intrinsic_quality_score(self, processed_data, labels, n_clusters):
        """计算内在质量得分"""
        
        score = 0
        
        if n_clusters >= 2 and len(set(labels)) >= 2:
            try:
                # 轮廓系数
                silhouette = silhouette_score(processed_data, labels)
                score += silhouette * 10
                
                # Calinski-Harabasz指数
                calinski = calinski_harabasz_score(processed_data, labels)
                # 归一化到0-10范围
                normalized_calinski = min(10, calinski / 100)
                score += normalized_calinski
                
            except:
                pass
        
        return max(0, score)
    
    def calculate_feature_consistency_score(self, features, n_clusters, noise_ratio):
        """计算特征一致性得分"""
        
        score = 0
        
        # 基于变异系数的一致性
        cv = features['cv']
        if cv < 0.2 and n_clusters <= 1:
            score += 5
        elif 0.3 < cv < 0.8 and n_clusters == 2:
            score += 5
        elif cv > 0.6 and n_clusters >= 3:
            score += 5
        
        # 基于密度峰的一致性
        peaks = features['density_peaks']
        if peaks <= 2 and n_clusters <= 1:
            score += 3
        elif 2 < peaks <= 4 and n_clusters == 2:
            score += 3
        elif peaks > 3 and n_clusters >= 3:
            score += 3
        
        # 基于Hopkins统计量的一致性
        hopkins = features['hopkins_statistic']
        if hopkins > 0.7:  # 强聚类倾向
            if n_clusters >= 2:
                score += 2
        else:  # 弱聚类倾向
            if n_clusters <= 1:
                score += 2
        
        return score
    
    def calculate_robustness_score(self, noise_ratio, n_clusters, data_size):
        """计算鲁棒性得分"""
        
        score = 0
        
        # 噪声比例合理性
        if noise_ratio < 0.1:
            score += 3
        elif noise_ratio < 0.2:
            score += 2
        elif noise_ratio < 0.3:
            score += 1
        
        # 聚类数量合理性
        if 1 <= n_clusters <= min(5, data_size // 20):
            score += 2
        
        return score
    
    def validate_and_refine_parameters(self, processed_data, best_params, features, expected_pattern):
        """验证和精化参数"""
        
        if not best_params:
            return None
        
        # 在最优参数附近进行精细搜索
        base_eps = best_params['eps']
        base_min_samples = best_params['min_samples']
        
        refined_eps_range = np.arange(max(0.05, base_eps - 0.02), base_eps + 0.03, 0.01)
        refined_min_samples_range = [max(3, base_min_samples - 1), base_min_samples, base_min_samples + 1]
        
        refined_best_score = best_params['score']
        refined_best_params = best_params
        
        for eps in refined_eps_range:
            for min_samples in refined_min_samples_range:
                if min_samples < len(processed_data) // 8:
                    try:
                        dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                        labels = dbscan.fit_predict(processed_data)
                        
                        score = self.calculate_clustering_quality_score(
                            processed_data, labels, features, expected_pattern
                        )
                        
                        if score > refined_best_score:
                            refined_best_score = score
                            refined_best_params = {
                                'eps': eps,
                                'min_samples': min_samples,
                                'labels': labels,
                                'score': score,
                                'n_clusters': len(set(labels)) - (1 if -1 in labels else 0),
                                'noise_ratio': (labels == -1).sum() / len(labels)
                            }
                    
                    except:
                        continue
        
        print(f"  精化后参数: eps={refined_best_params['eps']:.3f}, min_samples={refined_best_params['min_samples']}")
        print(f"  最终得分: {refined_best_params['score']:.2f}")
        
        return refined_best_params

    def apply_to_real_data(self, system, target_sites=None):
        """应用到真实数据"""

        print("\n🔧 应用DBSCAN优化到真实数据")
        print("-"*60)

        results = {}

        # 如果没有指定目标站点，处理所有站点
        if target_sites is None:
            target_sites = []
            for month in system.monthly_data.keys():
                month_data = system.monthly_data[month]
                for site_id in month_data['site_id'].unique():
                    target_sites.append({'site_id': site_id, 'month': month})

        for i, site_info in enumerate(target_sites[:10], 1):  # 限制处理数量用于演示
            site_id = site_info['site_id']
            month = site_info['month']

            print(f"\n处理站点 {i}: {site_id}_{month}月")

            # 获取站点数据
            month_data = system.monthly_data[month]
            site_data = month_data[month_data['site_id'] == site_id]

            if len(site_data) > 0:
                flow_data = site_data['flow_value']
                positive_data = flow_data[flow_data > 0]

                if len(positive_data) >= 20:
                    # 应用DBSCAN优化
                    result = self.optimize_dbscan_parameters(positive_data)

                    if result:
                        # 推断运行模式
                        inferred_pattern = self.infer_pattern_from_clustering(result, positive_data)

                        results[f"{site_id}_{month}"] = {
                            'site_id': site_id,
                            'month': month,
                            'optimal_params': {
                                'eps': result['eps'],
                                'min_samples': result['min_samples']
                            },
                            'clustering_result': {
                                'n_clusters': result['n_clusters'],
                                'noise_ratio': result['noise_ratio'],
                                'score': result['score']
                            },
                            'inferred_pattern': inferred_pattern,
                            'data_size': len(positive_data)
                        }

                        print(f"  ✅ 优化成功: {result['n_clusters']}个聚类, 推断模式: {inferred_pattern}")
                    else:
                        print(f"  ❌ 优化失败")
                else:
                    print(f"  ⚠️ 数据不足: 仅{len(positive_data)}个正值点")
            else:
                print(f"  ❌ 无数据")

        return results

    def infer_pattern_from_clustering(self, clustering_result, data):
        """从聚类结果推断运行模式"""

        n_clusters = clustering_result['n_clusters']
        noise_ratio = clustering_result['noise_ratio']

        # 计算数据特征
        cv = data.std() / data.mean() if data.mean() > 0 else 0
        zero_ratio = 0  # 这里处理的是正值数据

        # 推断逻辑
        if n_clusters == 0:
            return '数据不足或全为噪声'
        elif n_clusters == 1:
            if cv < 0.25:
                return '单状态稳定运行'
            else:
                return '正常波动'
        elif n_clusters == 2:
            if noise_ratio < 0.2 and cv > 0.3:
                return '双状态稳定运行'
            else:
                return '正常波动'
        elif n_clusters >= 3:
            if noise_ratio < 0.3 and cv > 0.5:
                return '多状态稳定运行'
            else:
                return '正常波动'
        else:
            return '未知模式'

    def generate_optimization_report(self, results, output_path):
        """生成优化报告"""

        print(f"\n📋 生成DBSCAN优化报告")
        print("-"*60)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("DBSCAN专用模式识别优化报告\n")
            f.write("="*80 + "\n\n")

            f.write(f"优化时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"处理站点数: {len(results)}\n\n")

            # 统计不同模式的分布
            pattern_stats = {}
            param_stats = {'eps': [], 'min_samples': []}

            for site_key, result in results.items():
                pattern = result['inferred_pattern']
                pattern_stats[pattern] = pattern_stats.get(pattern, 0) + 1

                param_stats['eps'].append(result['optimal_params']['eps'])
                param_stats['min_samples'].append(result['optimal_params']['min_samples'])

            f.write("识别的运行模式分布:\n")
            for pattern, count in sorted(pattern_stats.items()):
                f.write(f"  {pattern}: {count}个站点\n")

            f.write(f"\n优化参数统计:\n")
            f.write(f"  eps范围: {min(param_stats['eps']):.3f} - {max(param_stats['eps']):.3f}\n")
            f.write(f"  eps均值: {np.mean(param_stats['eps']):.3f}\n")
            f.write(f"  min_samples范围: {min(param_stats['min_samples'])} - {max(param_stats['min_samples'])}\n")
            f.write(f"  min_samples均值: {np.mean(param_stats['min_samples']):.1f}\n")

            f.write(f"\n详细结果:\n")
            f.write("-"*60 + "\n")

            for site_key, result in results.items():
                f.write(f"\n站点: {result['site_id']}_{result['month']}月\n")
                f.write(f"  数据量: {result['data_size']}个点\n")
                f.write(f"  最优参数: eps={result['optimal_params']['eps']:.3f}, min_samples={result['optimal_params']['min_samples']}\n")
                f.write(f"  聚类结果: {result['clustering_result']['n_clusters']}个聚类, 噪声比例={result['clustering_result']['noise_ratio']:.3f}\n")
                f.write(f"  质量得分: {result['clustering_result']['score']:.2f}\n")
                f.write(f"  推断模式: {result['inferred_pattern']}\n")

        print(f"✅ 报告已生成: {output_path}")

def apply_dbscan_optimization_to_system():
    """将DBSCAN优化应用到实际系统"""

    print("🎯 DBSCAN专用模式识别优化框架 - 实际应用")
    print("="*80)

    # 导入系统
    from 城市污染源异常检测系统 import CityAnomalyDetectionSystem

    # 初始化系统
    system = CityAnomalyDetectionSystem("唐山")
    system.load_and_preprocess_data()

    # 创建优化器
    optimizer = DBSCANPatternRecognitionOptimizer()

    # 应用到真实数据
    results = optimizer.apply_to_real_data(system)

    # 生成报告
    report_path = "检测报告/DBSCAN优化应用报告.txt"
    os.makedirs("检测报告", exist_ok=True)
    optimizer.generate_optimization_report(results, report_path)

    # 统计结果
    if results:
        print(f"\n📊 优化应用总结:")
        print(f"   成功处理: {len(results)}个站点")

        # 统计模式分布
        patterns = [r['inferred_pattern'] for r in results.values()]
        pattern_counts = {p: patterns.count(p) for p in set(patterns)}

        for pattern, count in sorted(pattern_counts.items()):
            print(f"   {pattern}: {count}个")

        # 统计参数分布
        eps_values = [r['optimal_params']['eps'] for r in results.values()]
        min_samples_values = [r['optimal_params']['min_samples'] for r in results.values()]

        print(f"\n📈 参数优化结果:")
        print(f"   eps范围: {min(eps_values):.3f} - {max(eps_values):.3f}")
        print(f"   min_samples范围: {min(min_samples_values)} - {max(min_samples_values)}")

    return results

def main():
    """主函数"""

    # 演示优化框架
    print("🎯 DBSCAN专用模式识别优化框架演示")
    print("="*80)

    # 创建优化器实例
    optimizer = DBSCANPatternRecognitionOptimizer()

    # 生成测试数据
    np.random.seed(42)

    # 测试不同类型的数据
    test_cases = [
        {
            'name': '单状态稳定运行',
            'data': pd.Series(np.random.normal(100, 5, 500)),
            'expected': '单状态稳定运行'
        },
        {
            'name': '双状态稳定运行',
            'data': pd.Series(np.concatenate([
                np.random.normal(100, 10, 300),
                np.random.normal(500, 20, 200)
            ])),
            'expected': '双状态稳定运行'
        },
        {
            'name': '正常波动',
            'data': pd.Series(np.random.normal(200, 80, 400)),
            'expected': '正常波动'
        }
    ]

    for case in test_cases:
        print(f"\n测试案例：{case['name']}")
        result = optimizer.optimize_dbscan_parameters(case['data'], case['expected'])

        if result:
            inferred = optimizer.infer_pattern_from_clustering(result, case['data'])
            print(f"✅ 优化成功")
            print(f"   最优eps: {result['eps']:.3f}")
            print(f"   最优min_samples: {result['min_samples']}")
            print(f"   识别聚类数: {result['n_clusters']}")
            print(f"   噪声比例: {result['noise_ratio']:.3f}")
            print(f"   推断模式: {inferred}")
            print(f"   期望模式: {case['expected']}")
            print(f"   匹配度: {'✅' if inferred == case['expected'] else '❌'}")
        else:
            print("❌ 优化失败")

    # 应用到实际系统（如果可用）
    try:
        print(f"\n" + "="*80)
        apply_dbscan_optimization_to_system()
    except Exception as e:
        print(f"\n实际系统应用跳过: {e}")

if __name__ == "__main__":
    main()
