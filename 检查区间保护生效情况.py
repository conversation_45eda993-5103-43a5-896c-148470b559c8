#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查区间保护是否生效
通过分析系统输出的Excel报告来验证保护区间机制
"""

import pandas as pd
import numpy as np
import os
import glob

def analyze_protection_effectiveness():
    """分析保护区间生效情况"""
    
    print("🔍 检查区间保护生效情况")
    print("="*60)
    
    # 查找最新的Excel报告
    report_pattern = "检测报告/*/时间段异常检测详细报告_*.xlsx"
    report_files = glob.glob(report_pattern)
    
    if not report_files:
        print("❌ 未找到Excel报告文件")
        return
    
    # 选择最新的报告
    latest_report = max(report_files, key=os.path.getctime)
    print(f"📁 分析报告: {latest_report}")
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(latest_report)
        sheet_names = excel_file.sheet_names
        print(f"📋 工作表: {sheet_names}")
        
        # 查找包含异常检测结果的工作表
        main_sheet = None
        for sheet in sheet_names:
            if '异常检测' in sheet or '检测结果' in sheet or sheet == 'Sheet1':
                main_sheet = sheet
                break
        
        if not main_sheet:
            main_sheet = sheet_names[0]  # 使用第一个工作表
        
        print(f"📊 使用工作表: {main_sheet}")
        df = pd.read_excel(latest_report, sheet_name=main_sheet)
        
        print(f"📈 数据形状: {df.shape}")
        print(f"📋 列名:")
        for i, col in enumerate(df.columns):
            print(f"   {i+1}. {col}")
        
        # 查找目标企业和监测点
        target_keywords = ['大唐', '陡河', '8号', '脱硝', 'B']
        
        if '企业名称' in df.columns and '监测点名称' in df.columns:
            # 筛选包含关键词的记录
            mask = pd.Series([False] * len(df))
            for keyword in target_keywords:
                mask |= df['企业名称'].str.contains(keyword, na=False)
                mask |= df['监测点名称'].str.contains(keyword, na=False)
            
            target_records = df[mask]
            
            if len(target_records) > 0:
                print(f"\n🎯 找到相关记录: {len(target_records)} 条")
                
                # 显示匹配的企业和监测点
                unique_combinations = target_records[['企业名称', '监测点名称']].drop_duplicates()
                for _, row in unique_combinations.iterrows():
                    company = row['企业名称']
                    site = row['监测点名称']
                    if all(keyword in str(company) + str(site) for keyword in ['大唐', '陡河', '8号']):
                        print(f"✅ 目标匹配: {company} - {site}")
                        
                        # 分析这个组合的数据
                        site_data = df[(df['企业名称'] == company) & (df['监测点名称'] == site)]
                        analyze_site_protection(site_data, company, site)
            else:
                print("⚠️  未找到完全匹配的记录，显示所有包含'大唐'的记录:")
                daqing_records = df[df['企业名称'].str.contains('大唐', na=False)]
                if len(daqing_records) > 0:
                    unique_companies = daqing_records['企业名称'].unique()
                    for company in unique_companies:
                        print(f"   - {company}")
                        company_sites = daqing_records[daqing_records['企业名称'] == company]['监测点名称'].unique()
                        for site in company_sites:
                            if '8号' in str(site):
                                print(f"     ✅ {site}")
        
        else:
            print("⚠️  Excel文件结构与预期不符，显示前几行数据:")
            print(df.head())
    
    except Exception as e:
        print(f"❌ 读取Excel文件出错: {e}")

def analyze_site_protection(site_data, company, site):
    """分析特定站点的保护区间效果"""
    
    print(f"\n📊 分析站点保护效果: {company} - {site}")
    print("="*60)
    
    if len(site_data) == 0:
        print("❌ 无数据")
        return
    
    # 查找时段相关的列
    segment_cols = [col for col in site_data.columns if '时段' in col or 'segment' in col.lower()]
    anomaly_cols = [col for col in site_data.columns if '异常' in col or 'anomaly' in col.lower()]
    protection_cols = [col for col in site_data.columns if '保护' in col or 'protection' in col.lower()]
    
    print(f"📋 时段相关列: {segment_cols}")
    print(f"📋 异常相关列: {anomaly_cols}")
    print(f"📋 保护相关列: {protection_cols}")
    
    # 如果有时段信息，分析每个时段
    if segment_cols:
        segment_col = segment_cols[0]
        segments = site_data[segment_col].unique()
        
        print(f"\n🔢 发现时段: {sorted(segments)}")
        
        # 重点分析时段4
        segment_4_data = site_data[site_data[segment_col] == 4]
        if len(segment_4_data) > 0:
            print(f"\n🎯 时段4详细分析:")
            print(f"   数据点数: {len(segment_4_data)}")
            
            # 分析异常情况
            if anomaly_cols:
                for anomaly_col in anomaly_cols:
                    if anomaly_col in segment_4_data.columns:
                        anomaly_count = segment_4_data[anomaly_col].sum() if segment_4_data[anomaly_col].dtype == bool else len(segment_4_data[segment_4_data[anomaly_col].notna()])
                        print(f"   {anomaly_col}: {anomaly_count} 个异常")
            
            # 分析保护情况
            if protection_cols:
                for protection_col in protection_cols:
                    if protection_col in segment_4_data.columns:
                        protection_info = segment_4_data[protection_col].iloc[0] if len(segment_4_data) > 0 else "无信息"
                        print(f"   {protection_col}: {protection_info}")
            
            # 显示具体数据
            print(f"\n📈 时段4数据样本:")
            display_cols = ['流量值', '异常标记', '保护区间', '异常类型'] if any(col in site_data.columns for col in ['流量值', '异常标记', '保护区间', '异常类型']) else site_data.columns[:5]
            available_cols = [col for col in display_cols if col in segment_4_data.columns]
            if available_cols:
                print(segment_4_data[available_cols].head(10))
            else:
                print(segment_4_data.head(3))
        else:
            print(f"⚠️  未找到时段4的数据")
    
    else:
        print(f"⚠️  未找到时段信息列")
        print(f"📈 显示前几行数据:")
        print(site_data.head())

def check_protection_mechanism_in_code():
    """检查代码中的保护机制实现"""
    
    print(f"\n🔧 检查代码中的保护机制实现")
    print("="*60)
    
    # 从V6.0系统代码中提取保护机制的关键参数
    protection_rules = {
        "调整系数规则": {
            "标准差 < 0.1": "调整系数 = 1.5 (扩大保护)",
            "标准差 0.1-0.5": "调整系数 = 1.0 (正常保护)", 
            "标准差 > 0.5": "调整系数 = 0.5 (缩小保护)"
        },
        "保护判断条件": {
            "受保护": "值 > 0 且 下界 ≤ 值 ≤ 上界",
            "不受保护": "负值或超出保护区间"
        },
        "保护区间计算": {
            "基准值": "时间段内正值数据的中位数",
            "保护范围": "标准差 × 调整系数",
            "下界": "max(0, 中位数 - 保护范围)",
            "上界": "中位数 + 保护范围"
        }
    }
    
    for category, rules in protection_rules.items():
        print(f"\n📋 {category}:")
        for rule_name, rule_desc in rules.items():
            print(f"   • {rule_name}: {rule_desc}")
    
    print(f"\n💡 保护机制的理论效果:")
    print(f"   ✅ 稳定时段(小标准差): 保护区间较大，减少误判")
    print(f"   ✅ 波动时段(大标准差): 保护区间较小，严格检测")
    print(f"   ✅ 负值异常: 不受保护，始终被检测")
    print(f"   ✅ 时段独立: 每个时段有独立的保护参数")

def provide_judgment_framework():
    """提供判断框架"""
    
    print(f"\n🎯 区间保护误判判断框架")
    print("="*60)
    
    judgment_criteria = [
        {
            "判断点": "保护区间大小是否合理",
            "检查方法": "保护范围/中位数 应在 10%-50% 之间",
            "误判风险": "过小可能误判正常波动，过大可能漏检真异常"
        },
        {
            "判断点": "异常点是否明显偏离",
            "检查方法": "观察异常点与正常数据的距离",
            "误判风险": "轻微偏离被判为异常可能是误判"
        },
        {
            "判断点": "时段运行状态是否一致",
            "检查方法": "检查时段内数据是否呈现一致的运行模式",
            "误判风险": "运行状态变化时可能产生误判"
        },
        {
            "判断点": "统计方法一致性",
            "检查方法": "P5/P95、IQR、MAD三种方法是否一致判断",
            "误判风险": "方法间差异大时需要人工判断"
        }
    ]
    
    for i, criteria in enumerate(judgment_criteria, 1):
        print(f"{i}. {criteria['判断点']}")
        print(f"   检查方法: {criteria['检查方法']}")
        print(f"   误判风险: {criteria['误判风险']}")
        print()
    
    print(f"🔍 针对时段4的具体检查建议:")
    print(f"   1. 查看图表中时段4的数据分布是否均匀")
    print(f"   2. 检查被标红的异常点是否明显偏离主体数据")
    print(f"   3. 观察保护区间(如果显示)是否覆盖了正常波动")
    print(f"   4. 对比相邻时段的异常检测结果")
    print(f"   5. 结合业务知识判断异常点是否合理")

if __name__ == "__main__":
    analyze_protection_effectiveness()
    check_protection_mechanism_in_code()
    provide_judgment_framework()
    
    print(f"\n📝 中立结论:")
    print(f"   区间保护机制在理论上是合理的，但具体到时段4是否存在误判，")
    print(f"   需要结合图表中的实际数据分布和异常标记情况进行综合判断。")
    print(f"   建议按照上述判断框架逐一检查。")
