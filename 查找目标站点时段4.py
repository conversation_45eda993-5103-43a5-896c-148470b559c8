#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找目标站点的时段4数据
"""

import pandas as pd
import numpy as np
import os

def find_target_site_segment4():
    """查找目标站点的时段4数据"""
    
    print("🔍 查找目标站点时段4数据")
    print("="*60)
    
    # 读取Excel报告
    report_file = "检测报告/07-31-18-29-27/时间段异常检测详细报告_07-31-18-29-27.xlsx"
    
    if not os.path.exists(report_file):
        print(f"❌ 报告文件不存在: {report_file}")
        return
    
    df = pd.read_excel(report_file, sheet_name='时间段分析汇总')
    print(f"📊 总记录数: {len(df)}")
    
    # 查找时段4的所有记录
    segment4_data = df[df['时间段'] == 4]
    print(f"📈 时段4记录数: {len(segment4_data)}")
    
    if len(segment4_data) > 0:
        print(f"\n📋 时段4的站点分布:")
        print(f"{'站点ID':<20} {'运行状态':<12} {'异常数量':<8} {'置信度':<8} {'数据点数':<8}")
        print("-" * 60)
        
        for _, row in segment4_data.iterrows():
            site_id = str(row['站点ID'])
            status = row['运行状态']
            anomaly_count = row['异常数量']
            confidence = row['置信度']
            data_count = row['数据点数']
            
            print(f"{site_id:<20} {status:<12} {anomaly_count:<8} {confidence:<8.3f} {data_count:<8}")
        
        # 查找异常数量较多的站点
        high_anomaly_sites = segment4_data[segment4_data['异常数量'] > 10]
        if len(high_anomaly_sites) > 0:
            print(f"\n⚠️  时段4异常数量较多的站点 (>10个):")
            for _, row in high_anomaly_sites.iterrows():
                site_id = str(row['站点ID'])
                anomaly_count = row['异常数量']
                status = row['运行状态']
                print(f"   站点ID: {site_id}, 异常数量: {anomaly_count}, 运行状态: {status}")
        
        # 分析异常数量分布
        anomaly_stats = segment4_data['异常数量'].describe()
        print(f"\n📊 时段4异常数量统计:")
        print(f"   平均值: {anomaly_stats['mean']:.1f}")
        print(f"   中位数: {anomaly_stats['50%']:.1f}")
        print(f"   最大值: {anomaly_stats['max']:.0f}")
        print(f"   最小值: {anomaly_stats['min']:.0f}")
        
        # 分析运行状态分布
        status_counts = segment4_data['运行状态'].value_counts()
        print(f"\n📈 时段4运行状态分布:")
        for status, count in status_counts.items():
            percentage = count / len(segment4_data) * 100
            print(f"   {status}: {count} 个站点 ({percentage:.1f}%)")
        
        # 查找可能的保护区间效果
        print(f"\n🔧 保护区间效果分析:")
        
        # 按运行状态分析异常数量
        for status in status_counts.index:
            status_data = segment4_data[segment4_data['运行状态'] == status]
            avg_anomalies = status_data['异常数量'].mean()
            print(f"   {status}状态: 平均异常数量 {avg_anomalies:.1f}")
        
        # 查找置信度与异常数量的关系
        high_confidence = segment4_data[segment4_data['置信度'] > 0.9]
        low_confidence = segment4_data[segment4_data['置信度'] < 0.7]
        
        if len(high_confidence) > 0:
            print(f"\n✅ 高置信度站点 (>0.9): {len(high_confidence)} 个")
            print(f"   平均异常数量: {high_confidence['异常数量'].mean():.1f}")
        
        if len(low_confidence) > 0:
            print(f"\n⚠️  低置信度站点 (<0.7): {len(low_confidence)} 个")
            print(f"   平均异常数量: {low_confidence['异常数量'].mean():.1f}")
    
    # 尝试读取原始数据来进一步分析
    try:
        data_file = "数据读取/唐山2025-05.xlsx"
        if os.path.exists(data_file):
            print(f"\n📁 尝试从原始数据分析...")
            raw_df = pd.read_excel(data_file)
            
            # 查找列名
            print(f"📋 原始数据列名: {list(raw_df.columns)}")
            
            # 查找包含"大唐"和"陡河"的企业
            if '企业名称' in raw_df.columns:
                target_companies = raw_df[raw_df['企业名称'].str.contains('大唐.*陡河', na=False, regex=True)]['企业名称'].unique()
                
                if len(target_companies) > 0:
                    print(f"\n🎯 找到目标企业:")
                    for company in target_companies:
                        print(f"   - {company}")
                        
                        # 查找该企业的8号脱硝B侧监测点
                        company_data = raw_df[raw_df['企业名称'] == company]
                        target_sites = company_data[company_data['监测点名称'].str.contains('8号.*脱.*B', na=False, regex=True)]['监测点名称'].unique()
                        
                        if len(target_sites) > 0:
                            print(f"     监测点:")
                            for site in target_sites:
                                print(f"       ✅ {site}")
                                
                                # 分析该监测点的数据
                                site_data = company_data[company_data['监测点名称'] == site]
                                print(f"         数据点数: {len(site_data)}")
                                
                                if '流量' in site_data.columns:
                                    flow_data = site_data['流量']
                                    print(f"         流量范围: {flow_data.min():.3f} - {flow_data.max():.3f}")
                                    print(f"         流量均值: {flow_data.mean():.3f}")
                                    print(f"         流量标准差: {flow_data.std():.3f}")
                                    
                                    # 简单的保护区间计算
                                    positive_flow = flow_data[flow_data > 0]
                                    if len(positive_flow) > 0:
                                        median_val = np.median(positive_flow)
                                        std_val = np.std(positive_flow)
                                        
                                        # 根据标准差确定调整系数
                                        if std_val < 0.1:
                                            adj_factor = 1.5
                                            protection_level = "扩大保护"
                                        elif std_val < 0.5:
                                            adj_factor = 1.0
                                            protection_level = "正常保护"
                                        else:
                                            adj_factor = 0.5
                                            protection_level = "缩小保护"
                                        
                                        protection_range = std_val * adj_factor
                                        lower_bound = max(0, median_val - protection_range)
                                        upper_bound = median_val + protection_range
                                        
                                        print(f"         保护区间: [{lower_bound:.3f}, {upper_bound:.3f}] ({protection_level})")
                                        
                                        # 计算保护区间相对大小
                                        relative_size = protection_range / median_val if median_val > 0 else 0
                                        print(f"         保护区间相对大小: {relative_size:.1%}")
                                        
                                        if relative_size < 0.1:
                                            print(f"         💡 保护区间可能过小，可能存在误判")
                                        elif relative_size > 0.5:
                                            print(f"         💡 保护区间可能过大，可能漏检")
                                        else:
                                            print(f"         ✅ 保护区间大小合理")
    
    except Exception as e:
        print(f"⚠️  原始数据分析出错: {e}")

def provide_segment4_judgment():
    """提供时段4的判断建议"""
    
    print(f"\n🎯 时段4区间保护误判判断建议")
    print("="*60)
    
    print(f"基于系统设计和数据分析，对时段4的判断建议:")
    print()
    
    print(f"1. 📊 数据特征检查:")
    print(f"   - 查看时段4的数据分布是否均匀")
    print(f"   - 检查是否存在明显的运行状态变化")
    print(f"   - 观察数据的波动程度")
    print()
    
    print(f"2. 🔧 保护机制验证:")
    print(f"   - 保护区间大小是否与数据波动匹配")
    print(f"   - 调整系数是否合理(基于标准差)")
    print(f"   - 中位数是否代表了正常运行水平")
    print()
    
    print(f"3. ⚠️  异常点检查:")
    print(f"   - 被标记为异常的点是否明显偏离")
    print(f"   - 异常点是否符合业务逻辑")
    print(f"   - 三种统计方法(P5/P95, IQR, MAD)是否一致")
    print()
    
    print(f"4. 🔍 对比分析:")
    print(f"   - 与相邻时段的异常检测结果对比")
    print(f"   - 与同类型站点的检测结果对比")
    print(f"   - 历史数据的异常模式对比")
    print()
    
    print(f"📝 中立结论:")
    print(f"   区间保护机制在理论上是科学合理的，采用了:")
    print(f"   • 中位数作为稳定基准")
    print(f"   • 反向调整系数适应不同波动程度")
    print(f"   • 时段独立计算避免跨时段干扰")
    print()
    print(f"   但具体到时段4是否存在误判，需要:")
    print(f"   • 结合图表中的实际数据点分布")
    print(f"   • 观察异常标记的合理性")
    print(f"   • 考虑该时段的运行状态特征")
    print()
    print(f"   可能存在误判的情况:")
    print(f"   • 保护区间过小导致正常波动被误判")
    print(f"   • 时段内运行状态发生变化")
    print(f"   • 统计方法对特殊分布的适应性问题")
    print()
    print(f"   可能没有误判的情况:")
    print(f"   • 异常点确实偏离正常运行范围")
    print(f"   • 保护区间设置合理且生效")
    print(f"   • 多种统计方法一致判断")

if __name__ == "__main__":
    find_target_site_segment4()
    provide_segment4_judgment()
    
    print(f"\n🎉 分析完成！")
    print(f"建议结合图表中时段4的具体标记情况进行最终判断。")
