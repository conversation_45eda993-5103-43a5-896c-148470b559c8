#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查变异系数数据范围
分析实际数据中变异系数的分布情况，确保纵坐标设置合理
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

def analyze_cv_data():
    """分析变异系数数据范围"""
    
    print("🔍 分析变异系数数据范围")
    print("="*50)
    
    # 加载数据
    data_file = "数据读取/唐山2025-05.xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    print(f"📁 加载数据文件: {data_file}")
    df = pd.read_excel(data_file)
    
    # 数据预处理
    df = df.dropna(subset=['流量'])
    df = df[df['流量'] >= 0]  # 过滤负值
    df['timestamp'] = pd.to_datetime(df['数据时间'])
    df['flow_value'] = df['流量']
    
    print(f"📊 有效数据记录: {len(df)} 条")
    
    # 按企业和监测点分组
    grouped = df.groupby(['企业名称', '监测点名称'])
    
    cv_stats = []
    
    print(f"\n📈 计算各站点的变异系数...")
    
    for (company, site), group_data in grouped:
        if len(group_data) < 50:  # 数据点太少跳过
            continue
            
        # 按时间排序
        group_data = group_data.sort_values('timestamp')
        flow_values = group_data['flow_value'].values
        
        # 计算滑动窗口变异系数
        window_size = max(24, len(flow_values) // 20)
        cv_values = []
        
        for i in range(window_size, len(flow_values)):
            window_data = flow_values[i-window_size:i]
            # 过滤掉零值和负值
            valid_data = window_data[window_data > 0]
            
            if len(valid_data) > 3:
                mean_val = np.mean(valid_data)
                std_val = np.std(valid_data)
                cv = std_val / mean_val if mean_val > 0 else 10.0
                cv_values.append(min(cv, 10.0))  # 限制最大值为10
            else:
                cv_values.append(10.0)
        
        if cv_values:
            cv_min = min(cv_values)
            cv_max = max(cv_values)
            cv_mean = np.mean(cv_values)
            cv_std = np.std(cv_values)
            
            cv_stats.append({
                'company': company[:20],
                'site': site[:20],
                'cv_min': cv_min,
                'cv_max': cv_max,
                'cv_mean': cv_mean,
                'cv_std': cv_std,
                'cv_range': cv_max - cv_min,
                'data_points': len(cv_values)
            })
    
    if not cv_stats:
        print("❌ 没有找到有效的变异系数数据")
        return
    
    # 转换为DataFrame进行分析
    cv_df = pd.DataFrame(cv_stats)
    
    print(f"\n📊 变异系数统计分析 (共{len(cv_df)}个站点):")
    print("="*60)
    
    print(f"最小值范围: {cv_df['cv_min'].min():.4f} - {cv_df['cv_min'].max():.4f}")
    print(f"最大值范围: {cv_df['cv_max'].min():.4f} - {cv_df['cv_max'].max():.4f}")
    print(f"平均值范围: {cv_df['cv_mean'].min():.4f} - {cv_df['cv_mean'].max():.4f}")
    print(f"变化范围: {cv_df['cv_range'].min():.4f} - {cv_df['cv_range'].max():.4f}")
    
    # 分析不同范围的站点数量
    print(f"\n📈 变异系数最大值分布:")
    ranges = [
        (0, 0.5, "极低 (0-0.5)"),
        (0.5, 1.0, "低 (0.5-1.0)"),
        (1.0, 2.0, "中等 (1.0-2.0)"),
        (2.0, 5.0, "高 (2.0-5.0)"),
        (5.0, 10.0, "极高 (5.0-10.0)")
    ]
    
    for min_val, max_val, label in ranges:
        count = len(cv_df[(cv_df['cv_max'] >= min_val) & (cv_df['cv_max'] < max_val)])
        percentage = count / len(cv_df) * 100
        print(f"   {label}: {count} 个站点 ({percentage:.1f}%)")
    
    # 找出可能有问题的纵坐标设置案例
    print(f"\n⚠️  需要特别关注的案例:")
    
    # 极小范围的案例
    small_range = cv_df[cv_df['cv_range'] < 0.1]
    if len(small_range) > 0:
        print(f"   变化范围很小 (<0.1): {len(small_range)} 个站点")
        for _, row in small_range.head(3).iterrows():
            print(f"     - {row['company']} | {row['site']}: [{row['cv_min']:.4f}, {row['cv_max']:.4f}]")
    
    # 极大范围的案例
    large_range = cv_df[cv_df['cv_range'] > 5.0]
    if len(large_range) > 0:
        print(f"   变化范围很大 (>5.0): {len(large_range)} 个站点")
        for _, row in large_range.head(3).iterrows():
            print(f"     - {row['company']} | {row['site']}: [{row['cv_min']:.4f}, {row['cv_max']:.4f}]")
    
    # 测试当前纵坐标设置逻辑
    print(f"\n🧪 测试当前纵坐标设置逻辑:")
    print("="*60)
    
    problematic_cases = []
    
    for _, row in cv_df.iterrows():
        cv_min = row['cv_min']
        cv_max = row['cv_max']
        cv_range = row['cv_range']
        
        if cv_range > 0:
            # 当前逻辑
            y_lower = max(0, cv_min - cv_range * 0.05)
            y_upper = cv_max + cv_range * 0.1
            if y_upper < 0.8:
                y_upper = max(y_upper, 0.8)
            y_upper = min(y_upper, 10)
        else:
            # 单一值情况
            if cv_max <= 0.1:
                y_lower, y_upper = 0, max(0.3, cv_max + 0.1)
            elif cv_max <= 1.0:
                y_lower, y_upper = max(0, cv_max - 0.1), cv_max + 0.2
            else:
                y_lower, y_upper = max(0, cv_max - 0.2), cv_max + 0.3
        
        # 检查是否能完整显示数据
        if y_lower > cv_min or y_upper < cv_max:
            problematic_cases.append({
                'company': row['company'],
                'site': row['site'],
                'cv_range': [cv_min, cv_max],
                'y_range': [y_lower, y_upper],
                'issue': 'Data not fully visible'
            })
        
        # 检查是否浪费太多空间
        total_range = y_upper - y_lower
        data_range = cv_max - cv_min if cv_range > 0 else 0.1
        waste_ratio = (total_range - data_range) / total_range
        
        if waste_ratio > 0.7:  # 超过70%的空间浪费
            problematic_cases.append({
                'company': row['company'],
                'site': row['site'],
                'cv_range': [cv_min, cv_max],
                'y_range': [y_lower, y_upper],
                'issue': f'Too much waste: {waste_ratio:.1%}'
            })
    
    if problematic_cases:
        print(f"⚠️  发现 {len(problematic_cases)} 个潜在问题案例:")
        for case in problematic_cases[:5]:  # 只显示前5个
            print(f"   - {case['company']} | {case['site']}")
            print(f"     数据范围: [{case['cv_range'][0]:.4f}, {case['cv_range'][1]:.4f}]")
            print(f"     Y轴范围: [{case['y_range'][0]:.4f}, {case['y_range'][1]:.4f}]")
            print(f"     问题: {case['issue']}")
    else:
        print("✅ 当前纵坐标设置逻辑看起来合理")
    
    # 生成建议
    print(f"\n💡 优化建议:")
    print("="*60)
    
    # 基于实际数据分析给出建议
    overall_cv_max = cv_df['cv_max'].max()
    overall_cv_min = cv_df['cv_min'].min()
    
    print(f"1. 数据范围: [{overall_cv_min:.4f}, {overall_cv_max:.4f}]")
    print(f"2. 建议上方留白: 10-15% (当前10%)")
    print(f"3. 建议下方留白: 5% (当前5%)")
    print(f"4. 建议最小显示范围: 0.2 (避免过度压缩)")
    print(f"5. 建议最大显示范围: {min(10, overall_cv_max * 1.2):.1f}")

if __name__ == "__main__":
    analyze_cv_data()
    print(f"\n🎯 分析完成！")
    print(f"请根据以上分析结果调整纵坐标设置逻辑。")
