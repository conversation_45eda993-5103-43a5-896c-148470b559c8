#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V6.0污染源异常检测系统 - 基于时间段的简化运行状态识别和异常检测
1. 时间段划分：按绿色虚线划分的时间段进行独立分析
2. 简化运行状态：停运状态、单状态运行、正常波动（移除双状态和多状态）
3. 异常检测简化：完全停用LOF和DBSCAN，仅使用统计方法
4. 新增区间保护机制：基于中位数和标准差的动态保护区间
5. 时间段状态标识：在散点图中添加每个时间段的运行状态标注
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter
from datetime import datetime
sys.path.append('.')

from 城市污染源异常检测系统 import CityAnomalyDetectionSystem

def implement_v6_time_segment_optimization():
    """实施V6.0基于时间段的简化异常检测系统"""

    print("🔧 V6.0基于时间段的简化异常检测系统")
    print("="*80)

    # 初始化系统
    system = CityAnomalyDetectionSystem("唐山")
    system.load_and_preprocess_data()

    # 执行基于时间段的分析
    segment_results = perform_time_segment_analysis(system)

    # 生成简化的可视化（带时间段状态标识）
    chart_results = generate_time_segment_visualizations(system, segment_results)

    # 生成简化报告
    generate_time_segment_report(segment_results, chart_results)

    return {
        'segment_results': segment_results,
        'charts': chart_results
    }

def perform_time_segment_analysis(system):
    """执行基于时间段的分析"""

    print("\n🔍 执行基于时间段的分析")
    print("-"*60)

    segment_results = {}

    for month in system.monthly_data.keys():
        print(f"\n处理{month}月数据...")
        month_data = system.monthly_data[month]
        month_results = {}

        # 按站点分组处理
        for site_id, site_data in month_data.groupby('site_id'):
            # 获取时间段分割点
            breakpoints = detect_time_segments(site_data)

            # 分析每个时间段
            segment_analysis = analyze_time_segments(site_data, breakpoints)

            month_results[site_id] = {
                'breakpoints': breakpoints,
                'segments': segment_analysis,
                'total_segments': len(segment_analysis)
            }

        segment_results[month] = month_results
        processed_sites = len(month_results)
        print(f"  完成{processed_sites}个站点的时间段分析")

    return segment_results

def detect_time_segments(site_data):
    """检测时间段分割点（基于现有系统的间断点检测逻辑）"""

    if len(site_data) < 48:  # 数据量太少
        return []

    # 按时间排序
    site_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)
    flow_values = site_data_sorted['flow_value'].values
    timestamps = site_data_sorted['timestamp'].values

    # 使用滑动窗口检测变异系数变化
    window_size = 24
    cv_threshold = 0.8
    fixed_threshold = 0.5

    breakpoints = []

    for i in range(window_size, len(flow_values) - window_size):
        # 计算前后窗口的变异系数
        before_window = flow_values[i-window_size:i]
        after_window = flow_values[i:i+window_size]

        # 过滤负值和零值进行变异系数计算
        before_positive = before_window[before_window > 0]
        after_positive = after_window[after_window > 0]

        if len(before_positive) > 5 and len(after_positive) > 5:
            cv_before = np.std(before_positive) / np.mean(before_positive)
            cv_after = np.std(after_positive) / np.mean(after_positive)

            # 三重条件检测
            condition1 = abs(cv_before - cv_after) > cv_threshold
            condition2 = abs(np.mean(before_positive) - np.mean(after_positive)) > fixed_threshold
            condition3 = abs(np.median(before_positive) - np.median(after_positive)) > fixed_threshold

            if condition1 and condition2 and condition3:
                breakpoints.append(timestamps[i])

    # 距离过滤（12点最小间隔）
    if len(breakpoints) > 1:
        filtered_breakpoints = [breakpoints[0]]
        for bp in breakpoints[1:]:
            if abs((pd.Timestamp(bp) - pd.Timestamp(filtered_breakpoints[-1])).total_seconds()) > 12 * 3600:
                filtered_breakpoints.append(bp)
        breakpoints = filtered_breakpoints

    # 最多8个时间段（7个分割点）
    if len(breakpoints) > 7:
        breakpoints = breakpoints[:7]

    return breakpoints

def analyze_time_segments(site_data, breakpoints):
    """分析每个时间段的运行状态和异常"""

    # 按时间排序
    site_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)
    timestamps = site_data_sorted['timestamp'].values
    flow_values = site_data_sorted['flow_value'].values

    # 创建时间段边界
    segment_boundaries = [timestamps[0]]  # 开始时间

    # 添加间断点作为分割边界
    for bp_time in breakpoints:
        # 找到最接近间断点的数据点
        time_diffs = [abs((pd.Timestamp(t) - pd.Timestamp(bp_time)).total_seconds()) for t in timestamps]
        closest_idx = np.argmin(time_diffs)
        if closest_idx < len(timestamps) - 1:  # 确保不是最后一个点
            segment_boundaries.append(timestamps[closest_idx])

    segment_boundaries.append(timestamps[-1])  # 结束时间
    segment_boundaries = sorted(list(set(segment_boundaries)))  # 去重并排序

    segments = []

    # 分析每个时间段
    for i in range(len(segment_boundaries) - 1):
        start_time = segment_boundaries[i]
        end_time = segment_boundaries[i + 1]

        # 找到该时间段的数据
        mask = (timestamps >= start_time) & (timestamps <= end_time)
        segment_data = site_data_sorted[mask].copy()

        if len(segment_data) == 0:
            continue

        # 判定该时间段的运行状态
        segment_status = classify_segment_status_simplified(segment_data['flow_value'])

        # 检测该时间段的异常
        segment_anomalies = detect_segment_anomalies_simplified(segment_data, segment_status)

        # 计算保护区间
        protection_interval = calculate_protection_interval(segment_data['flow_value'])

        segments.append({
            'segment_id': i + 1,
            'start_time': start_time,
            'end_time': end_time,
            'data_count': len(segment_data),
            'status': segment_status,
            'anomalies': segment_anomalies,
            'protection_interval': protection_interval,
            'data': segment_data
        })

    return segments

def classify_segment_status_simplified(flow_data):
    """简化的时间段运行状态分类（3种状态）"""

    total_count = len(flow_data)
    if total_count == 0:
        return {'status': '无数据', 'confidence': 0.0}

    # 计算零值或极低值比例
    low_value_count = (flow_data <= 0.2).sum()
    low_value_ratio = low_value_count / total_count

    # 按优先级顺序判定

    # 1. 停运状态：零值或极低值比例 ≥ 70%
    if low_value_ratio >= 0.7:
        return {
            'status': '停运状态',
            'confidence': min(0.95, 0.5 + low_value_ratio),
            'low_value_ratio': low_value_ratio,
            'description': f'零值或极低值比例{low_value_ratio:.1%}，判定为停运状态'
        }

    # 2. 对于非停运状态，计算变异系数
    positive_data = flow_data[flow_data > 0.2]
    if len(positive_data) < 3:
        return {
            'status': '停运状态',
            'confidence': 0.8,
            'low_value_ratio': low_value_ratio,
            'description': '有效数据点过少，判定为停运状态'
        }

    cv = np.std(positive_data) / np.mean(positive_data)

    # 3. 单状态运行：变异系数 < 0.3
    if cv < 0.3:
        return {
            'status': '单状态运行',
            'confidence': min(0.95, 0.6 + (0.3 - cv)),
            'cv': cv,
            'low_value_ratio': low_value_ratio,
            'description': f'变异系数{cv:.3f}，数据相对稳定，判定为单状态运行'
        }

    # 4. 正常波动：变异系数 ≥ 0.3
    else:
        return {
            'status': '正常波动',
            'confidence': min(0.95, 0.5 + min(cv, 1.0)),
            'cv': cv,
            'low_value_ratio': low_value_ratio,
            'description': f'变异系数{cv:.3f}，存在明显变化，判定为正常波动'
        }

def calculate_protection_interval(flow_data):
    """计算区间保护机制的保护区间"""

    # 过滤负值，只对正值数据计算保护区间
    positive_data = flow_data[flow_data > 0]

    if len(positive_data) < 3:
        return {'lower': 0, 'upper': 0, 'median': 0, 'std': 0, 'adjustment_factor': 0}

    median_val = np.median(positive_data)
    std_val = np.std(positive_data)

    # 根据标准差大小反向调整系数
    if std_val < 0.1:
        adjustment_factor = 1.5  # 标准差小，保护区间扩大
    elif std_val < 0.5:
        adjustment_factor = 1.0  # 标准差中等，正常保护区间
    else:
        adjustment_factor = 0.5  # 标准差大，保护区间缩小

    # 计算保护区间
    protection_range = std_val * adjustment_factor
    lower_bound = max(0, median_val - protection_range)  # 不包含负值
    upper_bound = median_val + protection_range

    return {
        'lower': lower_bound,
        'upper': upper_bound,
        'median': median_val,
        'std': std_val,
        'adjustment_factor': adjustment_factor,
        'description': f'保护区间: [{lower_bound:.3f}, {upper_bound:.3f}]'
    }

def detect_segment_anomalies_simplified(segment_data, segment_status):
    """简化的时间段异常检测（仅使用统计方法）"""

    flow_values = segment_data['flow_value'].values
    timestamps = segment_data['timestamp'].values

    if len(flow_values) < 3:
        return {
            'anomaly_indices': [],
            'anomaly_scores': {},
            'methods_used': [],
            'total_anomalies': 0
        }

    # 业务规则层：负值异常检测
    negative_anomalies = []
    for i, val in enumerate(flow_values):
        if val < 0:
            negative_anomalies.append(i)

    # 统计方法层：仅使用三种等权重统计方法
    statistical_anomalies = []
    anomaly_scores = {}
    methods_used = []

    # 过滤正值数据用于统计计算
    positive_mask = flow_values > 0
    if np.sum(positive_mask) >= 3:
        positive_values = flow_values[positive_mask]
        positive_indices = np.where(positive_mask)[0]

        # 1. P5/P95百分位数阈值法
        try:
            p5 = np.percentile(positive_values, 5)
            p95 = np.percentile(positive_values, 95)
            p5_p95_anomalies = []
            for i, val in enumerate(flow_values):
                if val > 0 and (val < p5 or val > p95):
                    p5_p95_anomalies.append(i)
                    anomaly_scores[i] = anomaly_scores.get(i, 0) + 3  # 等权重评分
            methods_used.append('P5/P95')
        except:
            p5_p95_anomalies = []

        # 2. IQR四分位距法
        try:
            q1 = np.percentile(positive_values, 25)
            q3 = np.percentile(positive_values, 75)
            iqr = q3 - q1
            iqr_lower = q1 - 1.5 * iqr
            iqr_upper = q3 + 1.5 * iqr
            iqr_anomalies = []
            for i, val in enumerate(flow_values):
                if val > 0 and (val < iqr_lower or val > iqr_upper):
                    iqr_anomalies.append(i)
                    anomaly_scores[i] = anomaly_scores.get(i, 0) + 3  # 等权重评分
            methods_used.append('IQR')
        except:
            iqr_anomalies = []

        # 3. MAD中位数绝对偏差法
        try:
            median_val = np.median(positive_values)
            mad = np.median(np.abs(positive_values - median_val))
            if mad > 0:
                mad_lower = median_val - 2.5 * mad
                mad_upper = median_val + 2.5 * mad
                mad_anomalies = []
                for i, val in enumerate(flow_values):
                    if val > 0 and (val < mad_lower or val > mad_upper):
                        mad_anomalies.append(i)
                        anomaly_scores[i] = anomaly_scores.get(i, 0) + 3  # 等权重评分
                methods_used.append('MAD')
            else:
                mad_anomalies = []
        except:
            mad_anomalies = []

        # 合并统计异常
        statistical_anomalies = list(set(p5_p95_anomalies + iqr_anomalies + mad_anomalies))

    # 应用区间保护机制
    protection_interval = calculate_protection_interval(pd.Series(flow_values))
    protected_anomalies = []

    for idx in statistical_anomalies:
        val = flow_values[idx]
        # 如果值在保护区间内且为正值，则不判定为异常
        if val > 0 and protection_interval['lower'] <= val <= protection_interval['upper']:
            continue  # 受保护，不判定为异常
        else:
            protected_anomalies.append(idx)

    # 合并所有异常
    all_anomalies = list(set(negative_anomalies + protected_anomalies))

    # 为负值异常添加高分
    for idx in negative_anomalies:
        anomaly_scores[idx] = anomaly_scores.get(idx, 0) + 5  # 负值异常高分

    return {
        'anomaly_indices': sorted(all_anomalies),
        'anomaly_scores': anomaly_scores,
        'methods_used': methods_used,
        'negative_anomalies': negative_anomalies,
        'statistical_anomalies': statistical_anomalies,
        'protected_anomalies': protected_anomalies,
        'protection_interval': protection_interval,
        'total_anomalies': len(all_anomalies)
    }

def generate_time_segment_visualizations(system, segment_results):
    """生成带时间段状态标识的散点图"""

    print("\n🎨 生成时间段可视化图表")
    print("-"*60)

    # 创建输出目录
    timestamp = datetime.now().strftime("%m-%d-%H-%M-%S")
    output_dir = f"检测报告/{timestamp}"
    os.makedirs(output_dir, exist_ok=True)

    chart_results = {}
    total_charts = 0

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    for month in segment_results.keys():
        print(f"\n生成{month}月图表...")
        month_charts = {}

        for site_id, site_result in segment_results[month].items():
            # 获取站点信息
            site_data = system.monthly_data[month][system.monthly_data[month]['site_id'] == site_id]
            if len(site_data) == 0:
                continue

            company_name = site_data.iloc[0]['company_name']
            site_name = site_data.iloc[0]['site_name']

            # 生成散点图
            chart_path = create_time_segment_scatter_plot(
                site_result, company_name, site_name, month, output_dir
            )

            if chart_path:
                month_charts[site_id] = {
                    'chart_path': chart_path,
                    'company_name': company_name,
                    'site_name': site_name,
                    'segments_count': site_result['total_segments']
                }
                total_charts += 1

        chart_results[month] = month_charts
        print(f"  完成{len(month_charts)}个站点的图表生成")

    print(f"\n📊 图表生成完成，共生成{total_charts}个散点图")
    print(f"📁 输出目录: {output_dir}")

    return {
        'output_dir': output_dir,
        'charts': chart_results,
        'total_charts': total_charts
    }

def create_time_segment_scatter_plot(site_result, company_name, site_name, month, output_dir):
    """创建带时间段状态标识的散点图"""

    try:
        segments = site_result['segments']
        if not segments:
            return None

        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 10))

        # 收集所有数据点
        all_timestamps = []
        all_flow_values = []
        all_colors = []
        all_markers = []
        all_sizes = []

        # 绿色虚线位置（时间段分割点）
        segment_boundaries = []

        # 处理每个时间段
        for i, segment in enumerate(segments):
            segment_data = segment['data']
            timestamps = pd.to_datetime(segment_data['timestamp'])
            flow_values = segment_data['flow_value'].values
            anomalies = segment['anomalies']

            # 添加时间段边界
            if i > 0:  # 不添加第一个时间段的开始边界
                segment_boundaries.append(timestamps.iloc[0])

            # 为每个数据点分配颜色和标记
            for j, (ts, val) in enumerate(zip(timestamps, flow_values)):
                all_timestamps.append(ts)
                all_flow_values.append(val)

                # 确定颜色和标记
                if j in anomalies['negative_anomalies']:
                    # 黄色叉号：负值异常
                    all_colors.append('yellow')
                    all_markers.append('x')
                    all_sizes.append(100)
                elif j in anomalies['anomaly_indices']:
                    # 判断是否为明显异常（高评分）
                    score = anomalies['anomaly_scores'].get(j, 0)
                    if score >= 6:  # 高评分异常
                        all_colors.append('red')
                        all_markers.append('o')
                        all_sizes.append(60)
                    else:
                        all_colors.append('yellow')
                        all_markers.append('o')
                        all_sizes.append(50)
                elif val == 0:
                    # 绿色圆点：零值
                    all_colors.append('green')
                    all_markers.append('o')
                    all_sizes.append(40)
                else:
                    # 蓝色圆点：正常值
                    all_colors.append('blue')
                    all_markers.append('o')
                    all_sizes.append(40)

        # 绘制数据点（按颜色分组以优化性能）
        color_groups = {'blue': [], 'yellow': [], 'red': [], 'green': []}
        marker_groups = {'blue': [], 'yellow': [], 'red': [], 'green': []}
        size_groups = {'blue': [], 'yellow': [], 'red': [], 'green': []}

        for i, (color, marker, size) in enumerate(zip(all_colors, all_markers, all_sizes)):
            color_groups[color].append(i)
            marker_groups[color].append(marker)
            size_groups[color].append(size)

        # 分别绘制每种颜色的点
        for color in ['blue', 'green', 'yellow', 'red']:
            if color_groups[color]:
                indices = color_groups[color]
                x_vals = [all_timestamps[i] for i in indices]
                y_vals = [all_flow_values[i] for i in indices]
                markers = [all_markers[i] for i in indices]
                sizes = [all_sizes[i] for i in indices]

                # 分别处理圆点和叉号
                circle_indices = [i for i, m in enumerate(markers) if m == 'o']
                x_indices = [i for i, m in enumerate(markers) if m == 'x']

                if circle_indices:
                    ax.scatter([x_vals[i] for i in circle_indices],
                             [y_vals[i] for i in circle_indices],
                             c=color, marker='o',
                             s=[sizes[i] for i in circle_indices],
                             alpha=0.7, edgecolors='black', linewidth=0.5)

                if x_indices:
                    ax.scatter([x_vals[i] for i in x_indices],
                             [y_vals[i] for i in x_indices],
                             c=color, marker='x',
                             s=[sizes[i] for i in x_indices],
                             alpha=0.8, linewidth=2)

        # 绘制绿色虚线（时间段分割线）
        for boundary in segment_boundaries:
            ax.axvline(x=boundary, color='green', linestyle='--', alpha=0.6, linewidth=2)

        # 添加时间段状态标识
        y_max = max(all_flow_values) if all_flow_values else 1
        y_text_pos = y_max * 0.9

        for i, segment in enumerate(segments):
            start_time = pd.to_datetime(segment['start_time'])
            end_time = pd.to_datetime(segment['end_time'])
            mid_time = start_time + (end_time - start_time) / 2

            status_text = segment['status']['status']
            confidence = segment['status']['confidence']

            # 在时间段中间添加状态标识
            ax.text(mid_time, y_text_pos - (i % 3) * y_max * 0.1,
                   f"段{i+1}: {status_text}\n(置信度:{confidence:.2f})",
                   ha='center', va='top', fontsize=10,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

        # 设置图表属性
        ax.set_xlabel('时间', fontsize=14)
        ax.set_ylabel('流量值', fontsize=14)
        ax.set_title(f'{company_name}-{site_name}-{month}月\n(时间段分析)', fontsize=16, fontweight='bold')

        # 自动调整纵坐标范围，确保完全显示内容
        if all_flow_values:
            y_min = min(all_flow_values)
            y_max = max(all_flow_values)
            y_range = y_max - y_min
            if y_range > 0:
                ax.set_ylim(y_min - y_range * 0.05, y_max + y_range * 0.15)  # 下方5%，上方15%留白
            else:
                ax.set_ylim(y_min - 1, y_max + 1)

        # 格式化x轴
        ax.xaxis.set_major_formatter(DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 设置坐标轴刻度朝内
        ax.tick_params(axis='both', direction='in', which='major')
        ax.tick_params(axis='both', direction='in', which='minor')

        # 添加网格
        ax.grid(True, alpha=0.3)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        safe_filename = f"{company_name}_{site_name}_{month}月_时间段分析.png"
        safe_filename = "".join(c for c in safe_filename if c.isalnum() or c in '._-')
        chart_path = os.path.join(output_dir, safe_filename)

        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    except Exception as e:
        print(f"生成散点图时出错: {e}")
        return None

def generate_time_segment_report(segment_results, chart_results):
    """生成时间段分析报告"""

    print("\n📋 生成时间段分析报告")
    print("-"*60)

    output_dir = chart_results['output_dir']

    # 生成方法说明文档
    method_doc_path = os.path.join(output_dir, "时间段异常检测方法说明.txt")
    generate_method_documentation(method_doc_path)

    # 生成Excel详细报告
    excel_path = os.path.join(output_dir, f"时间段异常检测详细报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
    generate_excel_report(segment_results, chart_results, excel_path)

    # 统计总体结果
    total_sites = 0
    total_segments = 0
    status_stats = {'停运状态': 0, '单状态运行': 0, '正常波动': 0}

    for month_results in segment_results.values():
        for site_result in month_results.values():
            total_sites += 1
            total_segments += site_result['total_segments']

            for segment in site_result['segments']:
                status = segment['status']['status']
                status_stats[status] = status_stats.get(status, 0) + 1

    print(f"\n📊 时间段分析统计:")
    print(f"  处理站点数: {total_sites}")
    print(f"  总时间段数: {total_segments}")
    print(f"  状态分布:")
    for status, count in status_stats.items():
        percentage = count / total_segments * 100 if total_segments > 0 else 0
        print(f"    {status}: {count}个 ({percentage:.1f}%)")

    print(f"\n📁 报告文件:")
    print(f"  方法说明: {method_doc_path}")
    print(f"  详细报告: {excel_path}")
    print(f"  散点图: {chart_results['total_charts']}个")

def generate_method_documentation(file_path):
    """生成方法说明文档"""

    content = """时间段异常检测方法说明 V6.0 (简化版)
===========================================

## 系统概述
本系统实现基于时间段的简化运行状态识别和异常检测，将复杂的多状态识别简化为3种基本状态，
并完全停用机器学习方法，仅使用统计方法进行异常检测。

## 1. 时间段划分方法
### 分割点检测算法
- 使用滑动窗口（24小时）检测变异系数变化
- 三重条件检测：变异系数差异 > 0.8，均值差异 > 0.5，中位数差异 > 0.5
- 最小间隔：12小时（避免过度分割）
- 最大时间段数：8个（7个分割点）

### 时间段标识
- 在散点图中用绿色虚线标识时间段分割点
- 每个时间段添加状态标识和置信度信息

## 2. 简化运行状态识别（3种状态）
### 停运状态
- 判定条件：零值或极低值（≤0.2）比例 ≥ 70%
- 数据特征：长期稳定在零值或极低值范围
- 置信度计算：0.5 + 零值比例（最高0.95）

### 单状态运行
- 判定条件：零值比例 < 70% 且 变异系数 < 0.3
- 数据特征：围绕某个中心值相对稳定波动
- 置信度计算：0.6 + (0.3 - 变异系数)（最高0.95）

### 正常波动
- 判定条件：零值比例 < 70% 且 变异系数 ≥ 0.3
- 数据特征：存在明显的规律性或随机性变化
- 置信度计算：0.5 + min(变异系数, 1.0)（最高0.95）

## 3. 简化异常检测方法
### 停用的方法
- ❌ LOF局部异常因子检测（完全停用）
- ❌ DBSCAN聚类检测（完全停用）
- ❌ 机器学习层检测（完全停用）

### 保留的统计方法（等权重）
#### P5/P95百分位数阈值法
- 计算正值数据的5%和95%分位数
- 异常判定：值 < P5 或 值 > P95
- 可信度评分：3分

#### IQR四分位距法
- 计算：Q1-1.5×IQR 和 Q3+1.5×IQR
- 异常判定：值在IQR范围外
- 可信度评分：3分

#### MAD中位数绝对偏差法
- 计算：中位数 ± 2.5×MAD
- 异常判定：值在MAD范围外
- 可信度评分：3分

### 业务规则层
- 负值异常：所有负值自动判定为异常
- 可信度评分：5分（最高优先级）

## 4. 区间保护机制
### 保护区间计算
- 基准值：该时间段内数据的中位数
- 调整系数：根据标准差反向调整
  * 标准差 < 0.1：调整系数 = 1.5（扩大保护区间）
  * 标准差 0.1-0.5：调整系数 = 1.0（正常保护区间）
  * 标准差 > 0.5：调整系数 = 0.5（缩小保护区间）
- 保护区间 = 中位数 ± (标准差 × 调整系数)

### 保护规则
- 落在保护区间内的正值不判定为异常
- 保护区间不包含负值（负值仍按业务规则判定为异常）
- 仅适用于正值数据的保护

## 5. 四色标记系统
- 🔵 蓝色圆点：正常值（通过所有检测）
- 🟡 黄色圆点：统计异常值（被统计方法检测出）
- 🟡 黄色叉号：负值异常（违反物理规律）
- 🔴 红色圆点：明显异常值（综合评分≥6分的高置信度异常）
- 🟢 绿色圆点：零值（特殊标记）

## 6. 系统优势
- 简化运行状态识别，提高识别准确性
- 停用复杂机器学习方法，降低误报率
- 基于时间段的独立分析，更符合实际运行模式
- 区间保护机制减少正常波动的误判
- 等权重统计方法，避免方法偏向性

## 7. 技术参数
- 时间段检测窗口：24小时
- 变异系数阈值：0.8
- 固定阈值：0.5
- 最小时间段间隔：12小时
- 最大时间段数：8个
- 停运状态阈值：70%零值比例
- 单状态运行阈值：变异系数<0.3
- 保护区间调整系数：0.5-1.5

生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
系统版本：V6.0 简化版
"""

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def generate_excel_report(segment_results, chart_results, excel_path):
    """生成Excel详细报告"""

    # 这里可以添加Excel报告生成逻辑
    # 由于篇幅限制，暂时创建一个简单的占位符
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 创建汇总表
        summary_data = []
        for month, month_results in segment_results.items():
            for site_id, site_result in month_results.items():
                for segment in site_result['segments']:
                    summary_data.append({
                        '月份': month,
                        '站点ID': site_id,
                        '时间段': segment['segment_id'],
                        '开始时间': segment['start_time'],
                        '结束时间': segment['end_time'],
                        '数据点数': segment['data_count'],
                        '运行状态': segment['status']['status'],
                        '置信度': segment['status']['confidence'],
                        '异常数量': segment['anomalies']['total_anomalies'],
                        '检测方法': ','.join(segment['anomalies']['methods_used'])
                    })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='时间段分析汇总', index=False)

def main():
    """主函数"""
    try:
        print("开始V6.0基于时间段的简化异常检测系统...")

        results = implement_v6_time_segment_optimization()

        print(f"\n🎉 V6.0时间段简化系统实施完成！")
        print(f"✅ 时间段划分完成")
        print(f"✅ 简化运行状态识别完成")
        print(f"✅ 统计方法异常检测完成")
        print(f"✅ 区间保护机制实施完成")
        print(f"✅ 时间段可视化完成")
        print(f"✅ 简化报告生成完成")

        # 统计结果
        total_sites = 0
        total_segments = 0
        for month_results in results['segment_results'].values():
            for site_result in month_results.values():
                total_sites += 1
                total_segments += site_result['total_segments']

        print(f"\n📊 处理统计:")
        print(f"   处理站点数: {total_sites}个")
        print(f"   分析时间段数: {total_segments}个")
        print(f"   生成图表数: {results['charts']['total_charts']}个")
        print(f"   输出位置: {results['charts']['output_dir']}")

    except Exception as e:
        print(f"\n❌ 系统实施过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    
    print("\n🚨 执行优化的异常检测")
    print("-"*60)
    
    optimized_results = {}
    
    for month in system.monthly_data.keys():
        print(f"\n处理{month}月异常检测...")
        month_data = system.monthly_data[month]
        month_results = {}
        
        for site_id, site_data in month_data.groupby('site_id'):
            if site_id in optimized_patterns[month]:
                pattern_info = optimized_patterns[month][site_id]
                pattern_type = pattern_info['pattern_type']
                
                # 根据运行模式选择异常检测方法
                anomalies = detect_anomalies_by_pattern_optimized(
                    site_data, pattern_type, pattern_info
                )
                
                month_results[site_id] = {
                    'pattern_type': pattern_type,
                    'anomalies': anomalies,
                    'anomaly_count': len(anomalies)
                }
        
        optimized_results[month] = month_results
        total_anomalies = sum(result['anomaly_count'] for result in month_results.values())
        print(f"  检测到{total_anomalies}个异常值")
    
    return optimized_results

def detect_anomalies_by_pattern_optimized(site_data, pattern_type, pattern_info):
    """基于运行模式的优化异常检测"""
    
    flow_data = site_data['flow_value']
    anomalies = []
    
    # 1. 负值异常检测
    negative_indices = site_data[site_data['flow_value'] < 0].index
    for idx in negative_indices:
        anomalies.append({
            'index': idx,
            'timestamp': site_data.loc[idx, 'timestamp'],
            'value': site_data.loc[idx, 'flow_value'],
            'anomaly_type': '负值异常',
            'detection_method': '业务规则检测'
        })
    
    # 2. 根据运行模式选择检测方法
    positive_data = flow_data[flow_data > 0]
    
    if len(positive_data) < 5:
        return anomalies
    
    if pattern_type == '正常波动':
        # 使用统计学方法
        anomalies.extend(detect_statistical_anomalies(site_data, positive_data))
    
    elif pattern_type in ['双状态稳定运行', '多状态稳定运行']:
        # 使用分状态统计学方法（完全停用LOF）
        anomalies.extend(detect_state_based_statistical_anomalies(
            site_data, positive_data, pattern_info
        ))
    
    elif pattern_type in ['单状态稳定运行', '停运+正常波动']:
        # 使用统计学方法
        anomalies.extend(detect_statistical_anomalies(site_data, positive_data))
    
    return anomalies

def detect_statistical_anomalies(site_data, positive_data):
    """统计学异常检测方法"""
    
    anomalies = []
    
    if len(positive_data) < 5:
        return anomalies
    
    # P5/P95方法
    p5 = positive_data.quantile(0.05)
    p95 = positive_data.quantile(0.95)
    
    # IQR方法
    q1 = positive_data.quantile(0.25)
    q3 = positive_data.quantile(0.75)
    iqr = q3 - q1
    iqr_lower = q1 - 1.5 * iqr
    iqr_upper = q3 + 1.5 * iqr
    
    # MAD方法
    median = positive_data.median()
    mad = np.median(np.abs(positive_data - median))
    mad_lower = median - 3 * mad
    mad_upper = median + 3 * mad
    
    # 检测异常
    positive_indices = site_data[site_data['flow_value'] > 0].index
    
    for idx in positive_indices:
        value = site_data.loc[idx, 'flow_value']
        
        # 综合判断
        is_anomaly = False
        methods = []
        
        if value < p5 or value > p95:
            is_anomaly = True
            methods.append('P5/P95')
        
        if value < iqr_lower or value > iqr_upper:
            is_anomaly = True
            methods.append('IQR')
        
        if mad > 0 and (value < mad_lower or value > mad_upper):
            is_anomaly = True
            methods.append('MAD')
        
        if is_anomaly:
            # 确定异常严重程度
            if len(methods) >= 2:
                anomaly_type = '明显异常'
            else:
                anomaly_type = '统计异常'
            
            anomalies.append({
                'index': idx,
                'timestamp': site_data.loc[idx, 'timestamp'],
                'value': value,
                'anomaly_type': anomaly_type,
                'detection_method': '+'.join(methods)
            })
    
    return anomalies

def detect_state_based_statistical_anomalies(site_data, positive_data, pattern_info):
    """分状态统计学异常检测方法"""
    
    anomalies = []
    
    # 获取聚类信息
    if 'cluster_info' not in pattern_info or pattern_info['cluster_info'] is None:
        # 如果没有聚类信息，使用全局统计方法
        return detect_statistical_anomalies(site_data, positive_data)
    
    cluster_info = pattern_info['cluster_info']
    labels = cluster_info['labels']
    unique_states = sorted(set(labels) - {-1})
    
    # 在每个状态内使用统计学方法
    for state_id in unique_states:
        state_mask = labels == state_id
        state_data = positive_data[state_mask]
        state_indices = positive_data.index[state_mask]
        
        if len(state_data) >= 3:
            # 在状态内使用统计学方法
            state_anomalies = detect_statistical_anomalies_for_state(
                site_data, state_data, state_indices, state_id
            )
            anomalies.extend(state_anomalies)
    
    return anomalies

def detect_statistical_anomalies_for_state(site_data, state_data, state_indices, state_id):
    """单个状态内的统计学异常检测"""
    
    anomalies = []
    
    if len(state_data) < 3:
        return anomalies
    
    # 使用更严格的统计方法
    # P10/P90方法（更严格）
    p10 = state_data.quantile(0.10)
    p90 = state_data.quantile(0.90)
    
    # IQR方法
    q1 = state_data.quantile(0.25)
    q3 = state_data.quantile(0.75)
    iqr = q3 - q1
    iqr_lower = q1 - 1.5 * iqr
    iqr_upper = q3 + 1.5 * iqr
    
    # 检测状态内异常
    for idx in state_indices:
        if idx in site_data.index:
            value = site_data.loc[idx, 'flow_value']
            
            is_anomaly = False
            methods = []
            
            if value < p10 or value > p90:
                is_anomaly = True
                methods.append('P10/P90')
            
            if iqr > 0 and (value < iqr_lower or value > iqr_upper):
                is_anomaly = True
                methods.append('IQR')
            
            if is_anomaly:
                anomalies.append({
                    'index': idx,
                    'timestamp': site_data.loc[idx, 'timestamp'],
                    'value': value,
                    'anomaly_type': '状态内异常',
                    'detection_method': f'状态{state_id}内{"+".join(methods)}'
                })
    
    return anomalies

def generate_optimized_visualizations(system, optimized_patterns, optimized_results):
    """生成优化的可视化图表"""

    print("\n🎨 生成优化的可视化图表")
    print("-"*60)

    # 创建按时间命名的输出文件夹
    timestamp = pd.Timestamp.now().strftime("%m-%d-%H-%M-%S")
    chart_dir = os.path.join("..", "检测报告", timestamp)
    os.makedirs(chart_dir, exist_ok=True)

    chart_results = {}
    total_charts = 0

    for month in system.monthly_data.keys():
        print(f"\n生成{month}月图表...")
        month_data = system.monthly_data[month]
        month_charts = {}

        for site_id, site_data in month_data.groupby('site_id'):
            if site_id in optimized_patterns[month] and site_id in optimized_results[month]:
                pattern_info = optimized_patterns[month][site_id]
                result_info = optimized_results[month][site_id]

                # 获取站点信息
                company_name = "未知企业"
                site_name = "未知站点"
                if hasattr(system, 'site_profiles') and site_id in system.site_profiles:
                    profile = system.site_profiles[site_id]
                    company_name = profile.get('company_name', '未知企业')
                    site_name = profile.get('site_name', '未知站点')

                # 生成优化图表
                chart_success = create_enhanced_five_color_chart(
                    site_data, site_id, month, company_name, site_name,
                    pattern_info, result_info, chart_dir
                )

                if chart_success:
                    month_charts[site_id] = {
                        'pattern_type': pattern_info['pattern_type'],
                        'anomaly_count': result_info['anomaly_count'],
                        'chart_generated': True
                    }
                    total_charts += 1

        chart_results[month] = month_charts
        print(f"  生成{len(month_charts)}个图表")

    print(f"\n✅ 总共生成{total_charts}个优化图表")
    return chart_results

def create_enhanced_five_color_chart(site_data, site_id, month, company_name, site_name, pattern_info, result_info, chart_dir):
    """创建增强的五色标记散点图"""

    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    plt.figure(figsize=(14, 8))

    # 按时间戳排序所有数据
    site_month_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)

    # 分离数据
    zero_data = site_month_data_sorted[site_month_data_sorted['flow_value'] == 0]
    positive_data = site_month_data_sorted[site_month_data_sorted['flow_value'] > 0]
    negative_data = site_month_data_sorted[site_month_data_sorted['flow_value'] < 0]

    # 1. 绘制零值（绿色圆点）- 新增
    if len(zero_data) > 0:
        plt.scatter(zero_data['timestamp'], zero_data['flow_value'],
                   c='green', alpha=0.7, s=25, label='零值',
                   marker='o', edgecolors='darkgreen', linewidth=0.5)

    # 2. 绘制正常值（蓝色）
    if len(positive_data) > 0:
        plt.scatter(positive_data['timestamp'], positive_data['flow_value'],
                   c='blue', alpha=0.6, s=20, label='正常值')

    # 3. 绘制负值异常（黑色细叉号）
    if len(negative_data) > 0:
        plt.scatter(negative_data['timestamp'], negative_data['flow_value'],
                   c='black', alpha=0.9, s=30,
                   label='负值异常', marker='x', linewidth=1.5)

    # 4. 标记优化后的异常值
    anomalies = result_info['anomalies']
    if anomalies:
        # 分类异常值
        light_anomaly_times = []
        light_anomaly_values = []
        moderate_anomaly_times = []
        moderate_anomaly_values = []
        significant_anomaly_times = []
        significant_anomaly_values = []

        for anomaly in anomalies:
            if anomaly['value'] > 0:  # 只处理正值异常
                timestamp = anomaly['timestamp']
                value = anomaly['value']
                anomaly_type = anomaly['anomaly_type']

                if anomaly_type == '明显异常':
                    significant_anomaly_times.append(timestamp)
                    significant_anomaly_values.append(value)
                elif anomaly_type in ['状态内异常', '统计异常']:
                    moderate_anomaly_times.append(timestamp)
                    moderate_anomaly_values.append(value)
                else:
                    light_anomaly_times.append(timestamp)
                    light_anomaly_values.append(value)

        # 绘制轻度异常（空心圆圈）
        if light_anomaly_times:
            plt.scatter(light_anomaly_times, light_anomaly_values,
                       c='none', alpha=0.8, s=35, label='统计异常(轻度)',
                       marker='o', edgecolors='orange', linewidth=1.2)

        # 绘制中度异常（填充圆圈）
        if moderate_anomaly_times:
            plt.scatter(moderate_anomaly_times, moderate_anomaly_values,
                       c='yellow', alpha=0.6, s=40, label='统计异常(中度)',
                       marker='o', edgecolors='orange', linewidth=1.5)

        # 绘制明显异常（红色圆圈）
        if significant_anomaly_times:
            plt.scatter(significant_anomaly_times, significant_anomaly_values,
                       c='red', alpha=0.9, s=50, label='明显异常值',
                       marker='o', edgecolors='darkred', linewidth=1)

    # 设置图表属性
    pattern_type = pattern_info['pattern_type']
    plt.title(f'{company_name}-{site_name}-{month}月\n运行模式: {pattern_type}', fontsize=14, fontweight='bold')
    plt.xlabel('时间戳（时间序列）', fontsize=12)
    plt.ylabel('流量值', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)

    # 自动调整纵坐标范围，确保完全显示内容
    if len(site_month_data_sorted) > 0:
        y_min = site_month_data_sorted['flow_value'].min()
        y_max = site_month_data_sorted['flow_value'].max()
        y_range = y_max - y_min
        if y_range > 0:
            plt.ylim(y_min - y_range * 0.05, y_max + y_range * 0.15)  # 下方5%，上方15%留白
        else:
            plt.ylim(y_min - 1, y_max + 1)

    # 设置坐标轴刻度朝内
    plt.gca().tick_params(axis='both', direction='in', which='major')
    plt.gca().tick_params(axis='both', direction='in', which='minor')

    # 设置时间轴格式
    if len(site_month_data_sorted) > 0:
        plt.gca().xaxis.set_major_formatter(DateFormatter('%m-%d %H:%M'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(site_month_data_sorted)//10)))
        plt.xticks(rotation=45)

        # 显示时间范围
        start_time = site_month_data_sorted['timestamp'].min()
        end_time = site_month_data_sorted['timestamp'].max()
        plt.figtext(0.02, 0.02, f'时间范围: {start_time.strftime("%Y-%m-%d %H:%M")} 至 {end_time.strftime("%Y-%m-%d %H:%M")}',
                   fontsize=8, alpha=0.7)

    # 保存图表
    safe_site_id = str(site_id).replace('/', '_').replace('\\\\', '_')[:20]
    chart_filename = f"{safe_site_id}_{month}月.png"
    chart_path = os.path.join(chart_dir, chart_filename)

    plt.tight_layout()
    plt.savefig(chart_path, dpi=150, bbox_inches='tight')
    plt.close()

    # 验证文件生成
    if os.path.exists(chart_path):
        file_size = os.path.getsize(chart_path)
        return True
    else:
        return False

def generate_comprehensive_optimization_report(optimized_patterns, optimized_results, chart_results):
    """生成全面优化报告"""

    print("\n📋 生成全面优化报告")
    print("-"*60)

    # 使用与图表相同的时间戳文件夹
    timestamp_folder = pd.Timestamp.now().strftime("%m-%d-%H-%M-%S")
    report_dir = os.path.join("..", "检测报告", timestamp_folder)
    os.makedirs(report_dir, exist_ok=True)

    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    report_path = os.path.join(report_dir, f"V6.0系统全面优化报告_{timestamp}.txt")

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("V6.0污染源异常检测系统全面优化报告\n")
        f.write("="*80 + "\n\n")

        f.write(f"优化时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"优化版本: V6.0全面优化版\n\n")

        # 1. 优化内容总结
        f.write("1. 优化内容总结\n")
        f.write("-"*40 + "\n")
        f.write("✅ 异常检测方法调整:\n")
        f.write("   - 正常波动运行模式: 使用统计学方法(P5/P95、IQR、MAD)\n")
        f.write("   - 双/多状态稳定运行: 完全停用LOF，改用分状态统计学方法\n")
        f.write("✅ 运行模式识别优化:\n")
        f.write("   - 基于误分类案例优化DBSCAN参数\n")
        f.write("   - 特殊案例规则处理\n")
        f.write("✅ 散点图可视化增强:\n")
        f.write("   - 新增绿色零值标记\n")
        f.write("   - 更新五色标记系统\n")
        f.write("✅ 月度独立模式识别验证:\n")
        f.write("   - 确认系统使用月度独立数据进行分析\n\n")

        # 2. 运行模式分布统计
        f.write("2. 优化后运行模式分布\n")
        f.write("-"*40 + "\n")
        pattern_stats = {}
        for month, patterns in optimized_patterns.items():
            for site_id, pattern_info in patterns.items():
                pattern_type = pattern_info['pattern_type']
                pattern_stats[pattern_type] = pattern_stats.get(pattern_type, 0) + 1

        for pattern, count in sorted(pattern_stats.items()):
            f.write(f"{pattern}: {count}个站点月度组合\n")
        f.write(f"总计: {sum(pattern_stats.values())}个站点月度组合\n\n")

        # 3. 异常检测效果统计
        f.write("3. 异常检测效果统计\n")
        f.write("-"*40 + "\n")
        total_anomalies = 0
        method_stats = {}

        for month, results in optimized_results.items():
            for site_id, result_info in results.items():
                total_anomalies += result_info['anomaly_count']
                pattern_type = result_info['pattern_type']
                method_stats[pattern_type] = method_stats.get(pattern_type, 0) + result_info['anomaly_count']

        f.write(f"总异常数: {total_anomalies}个\n")
        f.write("按运行模式分布:\n")
        for pattern, count in sorted(method_stats.items()):
            f.write(f"  {pattern}: {count}个异常\n")
        f.write("\n")

        # 4. 可视化生成统计
        f.write("4. 可视化生成统计\n")
        f.write("-"*40 + "\n")
        total_charts = sum(len(month_charts) for month_charts in chart_results.values())
        f.write(f"生成图表总数: {total_charts}个\n")
        f.write("按月份分布:\n")
        for month in sorted(chart_results.keys()):
            month_count = len(chart_results[month])
            f.write(f"  {month}月: {month_count}个图表\n")
        f.write("\n")

        # 5. 误分类案例处理记录
        f.write("5. 误分类案例处理记录\n")
        f.write("-"*40 + "\n")
        f.write("基于以下案例进行了模式识别优化:\n")
        f.write("- 被误识别为双状态/多状态，实际为单状态稳定运行: 12个案例\n")
        f.write("- 被误识别为双状态/多状态，实际为正常波动运行: 4个案例\n")
        f.write("- 被误识别为其他模式，实际为停运+正常波动: 1个案例\n")
        f.write("- 被误识别为其他模式，实际为双状态稳定运行: 1个案例\n\n")

        # 6. 技术参数
        f.write("6. 技术参数\n")
        f.write("-"*40 + "\n")
        f.write("DBSCAN优化参数:\n")
        f.write("  eps范围: 0.15-0.5\n")
        f.write("  min_samples范围: 3-15\n")
        f.write("统计学异常检测:\n")
        f.write("  正常波动模式: P5/P95 + IQR + MAD\n")
        f.write("  双/多状态模式: 分状态P10/P90 + IQR\n")
        f.write("可视化增强:\n")
        f.write("  五色标记: 绿色(零值) + 蓝色(正常) + 橙色(轻度异常) + 黄色(中度异常) + 红色(明显异常) + 黑色(负值异常)\n")
        f.write("  图表格式: 14x8, DPI=150, 时间序列横坐标\n")
        f.write("  输出位置: 检测报告/时间戳文件夹/\n")

    print(f"✅ 优化报告已生成: V6.0系统全面优化报告_{timestamp}.txt")

def main():
    """主函数"""
    try:
        print("开始V6.0基于时间段的简化异常检测系统...")

        results = implement_v6_time_segment_optimization()

        print(f"\n🎉 V6.0时间段简化系统实施完成！")
        print(f"✅ 时间段划分完成")
        print(f"✅ 简化运行状态识别完成")
        print(f"✅ 统计方法异常检测完成")
        print(f"✅ 区间保护机制实施完成")
        print(f"✅ 时间段可视化完成")
        print(f"✅ 简化报告生成完成")

        # 统计结果
        total_sites = 0
        total_segments = 0
        for month_results in results['segment_results'].values():
            for site_result in month_results.values():
                total_sites += 1
                total_segments += site_result['total_segments']

        print(f"\n📊 处理统计:")
        print(f"   处理站点数: {total_sites}个")
        print(f"   分析时间段数: {total_segments}个")
        print(f"   生成图表数: {results['charts']['total_charts']}个")
        print(f"   输出位置: {results['charts']['output_dir']}")

    except Exception as e:
        print(f"\n❌ 系统实施过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
