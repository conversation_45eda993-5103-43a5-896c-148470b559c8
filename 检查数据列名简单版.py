#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据文件的列名
"""

import pandas as pd
import os

def check_data_columns():
    """检查数据文件的列名"""
    
    data_file = "数据读取/唐山2025-05.xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    print(f"📁 加载数据文件: {data_file}")
    df = pd.read_excel(data_file)
    
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名:")
    for i, col in enumerate(df.columns):
        print(f"   {i+1}. '{col}'")
    
    print(f"\n📈 前3行数据:")
    print(df.head(3))
    
    # 查找包含"大唐"和"陡河"的企业
    if '企业名称' in df.columns:
        companies = df['企业名称'].unique()
        print(f"\n🏭 包含'大唐'和'陡河'的企业:")
        for company in companies:
            if '大唐' in str(company) and '陡河' in str(company):
                print(f"   - {company}")
                
                # 查找该企业的监测点
                company_data = df[df['企业名称'] == company]
                sites = company_data['监测点名称'].unique()
                print(f"     监测点:")
                for site in sites:
                    if '8号' in str(site) and ('脱销' in str(site) or '脱硝' in str(site)):
                        print(f"       ✅ {site}")
                    else:
                        print(f"       - {site}")

if __name__ == "__main__":
    check_data_columns()
