#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V6.0污染源异常检测系统 - 基于时间段的简化运行状态识别和异常检测
1. 时间段划分：按绿色虚线划分的时间段进行独立分析
2. 简化运行状态：停运状态、单状态运行、正常波动（移除双状态和多状态）
3. 异常检测简化：完全停用LOF和DBSCAN，仅使用统计方法
4. 新增区间保护机制：基于中位数和标准差的动态保护区间
5. 时间段状态标识：在散点图中添加每个时间段的运行状态标注
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter
from datetime import datetime
sys.path.append('.')

from 城市污染源异常检测系统 import CityAnomalyDetectionSystem

def implement_v6_time_segment_optimization():
    """实施V6.0基于时间段的简化异常检测系统"""

    print("🔧 V6.0基于时间段的简化异常检测系统")
    print("="*80)

    # 创建统一的时间戳和输出目录
    timestamp = datetime.now().strftime("%m-%d-%H-%M-%S")
    base_dir = r"C:\Users\<USER>\Documents\augment-projects\分析在线数据\检测报告"
    output_dir = os.path.join(base_dir, timestamp)
    os.makedirs(output_dir, exist_ok=True)

    print(f"📁 系统输出目录: {output_dir}")

    # 初始化系统
    system = CityAnomalyDetectionSystem("唐山")
    system.load_and_preprocess_data()

    # 执行基于时间段的分析
    segment_results = perform_time_segment_analysis(system)

    # 生成简化的可视化（带时间段状态标识）
    chart_results = generate_time_segment_visualizations_with_dir(system, segment_results, output_dir)

    # 生成简化报告
    generate_time_segment_report(segment_results, chart_results)

    return {
        'segment_results': segment_results,
        'charts': chart_results
    }

def perform_time_segment_analysis(system):
    """执行基于时间段的分析"""
    
    print("\n🔍 执行基于时间段的分析")
    print("-"*60)
    
    segment_results = {}
    
    for month in system.monthly_data.keys():
        print(f"\n处理{month}月数据...")
        month_data = system.monthly_data[month]
        month_results = {}
        
        # 按站点分组处理
        for site_id, site_data in month_data.groupby('site_id'):
            # 获取时间段分割点
            breakpoints = detect_time_segments(site_data)
            
            # 分析每个时间段
            segment_analysis = analyze_time_segments(site_data, breakpoints)
            
            month_results[site_id] = {
                'breakpoints': breakpoints,
                'segments': segment_analysis,
                'total_segments': len(segment_analysis)
            }
        
        segment_results[month] = month_results
        processed_sites = len(month_results)
        print(f"  完成{processed_sites}个站点的时间段分析")
    
    return segment_results

def detect_time_segments(site_data):
    """检测时间段分割点（基于现有系统的间断点检测逻辑）"""
    
    if len(site_data) < 48:  # 数据量太少
        return []
    
    # 按时间排序
    site_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)
    flow_values = site_data_sorted['flow_value'].values
    timestamps = site_data_sorted['timestamp'].values
    
    # 使用滑动窗口检测变异系数变化
    window_size = 24
    cv_threshold = 0.8
    fixed_threshold = 0.5
    
    breakpoints = []
    
    for i in range(window_size, len(flow_values) - window_size):
        # 计算前后窗口的变异系数
        before_window = flow_values[i-window_size:i]
        after_window = flow_values[i:i+window_size]
        
        # 过滤负值和零值进行变异系数计算
        before_positive = before_window[before_window > 0]
        after_positive = after_window[after_window > 0]
        
        if len(before_positive) > 5 and len(after_positive) > 5:
            cv_before = np.std(before_positive) / np.mean(before_positive)
            cv_after = np.std(after_positive) / np.mean(after_positive)
            
            # 三重条件检测
            condition1 = abs(cv_before - cv_after) > cv_threshold
            condition2 = abs(np.mean(before_positive) - np.mean(after_positive)) > fixed_threshold
            condition3 = abs(np.median(before_positive) - np.median(after_positive)) > fixed_threshold
            
            if condition1 and condition2 and condition3:
                breakpoints.append(timestamps[i])
    
    # 距离过滤（12点最小间隔）
    if len(breakpoints) > 1:
        filtered_breakpoints = [breakpoints[0]]
        for bp in breakpoints[1:]:
            if abs((pd.Timestamp(bp) - pd.Timestamp(filtered_breakpoints[-1])).total_seconds()) > 12 * 3600:
                filtered_breakpoints.append(bp)
        breakpoints = filtered_breakpoints
    
    # 最多8个时间段（7个分割点）
    if len(breakpoints) > 7:
        breakpoints = breakpoints[:7]
    
    return breakpoints

def analyze_time_segments(site_data, breakpoints):
    """分析每个时间段的运行状态和异常"""
    
    # 按时间排序
    site_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)
    timestamps = site_data_sorted['timestamp'].values
    flow_values = site_data_sorted['flow_value'].values
    
    # 创建时间段边界
    segment_boundaries = [timestamps[0]]  # 开始时间
    
    # 添加间断点作为分割边界
    for bp_time in breakpoints:
        # 找到最接近间断点的数据点
        time_diffs = [abs((pd.Timestamp(t) - pd.Timestamp(bp_time)).total_seconds()) for t in timestamps]
        closest_idx = np.argmin(time_diffs)
        if closest_idx < len(timestamps) - 1:  # 确保不是最后一个点
            segment_boundaries.append(timestamps[closest_idx])
    
    segment_boundaries.append(timestamps[-1])  # 结束时间
    segment_boundaries = sorted(list(set(segment_boundaries)))  # 去重并排序
    
    segments = []
    
    # 分析每个时间段
    for i in range(len(segment_boundaries) - 1):
        start_time = segment_boundaries[i]
        end_time = segment_boundaries[i + 1]
        
        # 找到该时间段的数据
        mask = (timestamps >= start_time) & (timestamps <= end_time)
        segment_data = site_data_sorted[mask].copy()
        
        if len(segment_data) == 0:
            continue
        
        # 判定该时间段的运行状态
        segment_status = classify_segment_status_simplified(segment_data['flow_value'])
        
        # 检测该时间段的异常
        segment_anomalies = detect_segment_anomalies_simplified(segment_data, segment_status)
        
        # 计算保护区间
        protection_interval = calculate_protection_interval(segment_data['flow_value'])
        
        segments.append({
            'segment_id': i + 1,
            'start_time': start_time,
            'end_time': end_time,
            'data_count': len(segment_data),
            'status': segment_status,
            'anomalies': segment_anomalies,
            'protection_interval': protection_interval,
            'data': segment_data
        })
    
    return segments

def classify_segment_status_simplified(flow_data):
    """简化的时间段运行状态分类（3种状态）"""
    
    total_count = len(flow_data)
    if total_count == 0:
        return {'status': '无数据', 'confidence': 0.0}
    
    # 计算零值或极低值比例
    low_value_count = (flow_data <= 0.2).sum()
    low_value_ratio = low_value_count / total_count
    
    # 按优先级顺序判定
    
    # 1. 停运状态：零值或极低值比例 ≥ 70%
    if low_value_ratio >= 0.7:
        return {
            'status': '停运状态',
            'confidence': min(0.95, 0.5 + low_value_ratio),
            'low_value_ratio': low_value_ratio,
            'description': f'零值或极低值比例{low_value_ratio:.1%}，判定为停运状态'
        }
    
    # 2. 对于非停运状态，计算变异系数
    positive_data = flow_data[flow_data > 0.2]
    if len(positive_data) < 3:
        return {
            'status': '停运状态',
            'confidence': 0.8,
            'low_value_ratio': low_value_ratio,
            'description': '有效数据点过少，判定为停运状态'
        }
    
    cv = np.std(positive_data) / np.mean(positive_data)
    
    # 3. 单状态运行：变异系数 < 0.3
    if cv < 0.3:
        return {
            'status': '单状态运行',
            'confidence': min(0.95, 0.6 + (0.3 - cv)),
            'cv': cv,
            'low_value_ratio': low_value_ratio,
            'description': f'变异系数{cv:.3f}，数据相对稳定，判定为单状态运行'
        }
    
    # 4. 正常波动：变异系数 ≥ 0.3
    else:
        return {
            'status': '正常波动',
            'confidence': min(0.95, 0.5 + min(cv, 1.0)),
            'cv': cv,
            'low_value_ratio': low_value_ratio,
            'description': f'变异系数{cv:.3f}，存在明显变化，判定为正常波动'
        }

def calculate_protection_interval(flow_data):
    """计算区间保护机制的保护区间"""
    
    # 过滤负值，只对正值数据计算保护区间
    positive_data = flow_data[flow_data > 0]
    
    if len(positive_data) < 3:
        return {'lower': 0, 'upper': 0, 'median': 0, 'std': 0, 'adjustment_factor': 0}
    
    median_val = np.median(positive_data)
    std_val = np.std(positive_data)
    
    # 根据标准差大小反向调整系数
    if std_val < 0.1:
        adjustment_factor = 1.5  # 标准差小，保护区间扩大
    elif std_val < 0.5:
        adjustment_factor = 1.0  # 标准差中等，正常保护区间
    else:
        adjustment_factor = 0.5  # 标准差大，保护区间缩小
    
    # 计算保护区间
    protection_range = std_val * adjustment_factor
    lower_bound = max(0, median_val - protection_range)  # 不包含负值
    upper_bound = median_val + protection_range
    
    return {
        'lower': lower_bound,
        'upper': upper_bound,
        'median': median_val,
        'std': std_val,
        'adjustment_factor': adjustment_factor,
        'description': f'保护区间: [{lower_bound:.3f}, {upper_bound:.3f}]'
    }

def detect_segment_anomalies_simplified(segment_data, segment_status):
    """简化的时间段异常检测（仅使用统计方法）"""

    flow_values = segment_data['flow_value'].values
    timestamps = segment_data['timestamp'].values

    if len(flow_values) < 3:
        return {
            'anomaly_indices': [],
            'anomaly_scores': {},
            'methods_used': [],
            'total_anomalies': 0
        }

    # 业务规则层：负值异常检测
    negative_anomalies = []
    for i, val in enumerate(flow_values):
        if val < 0:
            negative_anomalies.append(i)

    # 统计方法层：仅使用三种等权重统计方法
    statistical_anomalies = []
    anomaly_scores = {}
    methods_used = []

    # 过滤正值数据用于统计计算
    positive_mask = flow_values > 0
    if np.sum(positive_mask) >= 3:
        positive_values = flow_values[positive_mask]
        positive_indices = np.where(positive_mask)[0]

        # 1. P5/P95百分位数阈值法
        try:
            p5 = np.percentile(positive_values, 5)
            p95 = np.percentile(positive_values, 95)
            p5_p95_anomalies = []
            for i, val in enumerate(flow_values):
                if val > 0 and (val < p5 or val > p95):
                    p5_p95_anomalies.append(i)
                    anomaly_scores[i] = anomaly_scores.get(i, 0) + 3  # 等权重评分
            methods_used.append('P5/P95')
        except:
            p5_p95_anomalies = []

        # 2. IQR四分位距法
        try:
            q1 = np.percentile(positive_values, 25)
            q3 = np.percentile(positive_values, 75)
            iqr = q3 - q1
            iqr_lower = q1 - 1.5 * iqr
            iqr_upper = q3 + 1.5 * iqr
            iqr_anomalies = []
            for i, val in enumerate(flow_values):
                if val > 0 and (val < iqr_lower or val > iqr_upper):
                    iqr_anomalies.append(i)
                    anomaly_scores[i] = anomaly_scores.get(i, 0) + 3  # 等权重评分
            methods_used.append('IQR')
        except:
            iqr_anomalies = []

        # 3. MAD中位数绝对偏差法
        try:
            median_val = np.median(positive_values)
            mad = np.median(np.abs(positive_values - median_val))
            if mad > 0:
                mad_lower = median_val - 2.5 * mad
                mad_upper = median_val + 2.5 * mad
                mad_anomalies = []
                for i, val in enumerate(flow_values):
                    if val > 0 and (val < mad_lower or val > mad_upper):
                        mad_anomalies.append(i)
                        anomaly_scores[i] = anomaly_scores.get(i, 0) + 3  # 等权重评分
                methods_used.append('MAD')
            else:
                mad_anomalies = []
        except:
            mad_anomalies = []

        # 合并统计异常
        statistical_anomalies = list(set(p5_p95_anomalies + iqr_anomalies + mad_anomalies))

    # 应用区间保护机制
    protection_interval = calculate_protection_interval(pd.Series(flow_values))
    protected_anomalies = []

    for idx in statistical_anomalies:
        val = flow_values[idx]
        # 如果值在保护区间内且为正值，则不判定为异常
        if val > 0 and protection_interval['lower'] <= val <= protection_interval['upper']:
            continue  # 受保护，不判定为异常
        else:
            protected_anomalies.append(idx)

    # 合并所有异常
    all_anomalies = list(set(negative_anomalies + protected_anomalies))

    # 为负值异常添加高分
    for idx in negative_anomalies:
        anomaly_scores[idx] = anomaly_scores.get(idx, 0) + 5  # 负值异常高分

    return {
        'anomaly_indices': sorted(all_anomalies),
        'anomaly_scores': anomaly_scores,
        'methods_used': methods_used,
        'negative_anomalies': negative_anomalies,
        'statistical_anomalies': statistical_anomalies,
        'protected_anomalies': protected_anomalies,
        'protection_interval': protection_interval,
        'total_anomalies': len(all_anomalies)
    }

def generate_time_segment_visualizations_with_dir(system, segment_results, output_dir):
    """生成带时间段状态标识的散点图（使用预定义目录）"""

    print("\n🎨 生成时间段可视化图表")
    print("-"*60)
    print(f"📁 使用输出目录: {output_dir}")

    chart_results = {}
    total_charts = 0

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    for month in segment_results.keys():
        print(f"\n生成{month}月图表...")
        month_charts = {}

        for site_id, site_result in segment_results[month].items():
            # 获取站点信息
            site_data = system.monthly_data[month][system.monthly_data[month]['site_id'] == site_id]
            if len(site_data) == 0:
                continue

            company_name = site_data.iloc[0]['company_name']
            site_name = site_data.iloc[0]['site_name']

            # 生成三合一综合图表（参考07-31-16-13-17格式）
            chart_path = create_time_segment_comprehensive_chart(
                site_result, site_data, company_name, site_name, month, output_dir
            )

            if chart_path:
                month_charts[site_id] = {
                    'chart_path': chart_path,
                    'company_name': company_name,
                    'site_name': site_name,
                    'segments_count': site_result['total_segments']
                }
                total_charts += 1

        chart_results[month] = month_charts
        print(f"  完成{len(month_charts)}个站点的图表生成")

    print(f"\n📊 图表生成完成，共生成{total_charts}个散点图")
    print(f"📁 输出目录: {output_dir}")

    return {
        'output_dir': output_dir,
        'charts': chart_results,
        'total_charts': total_charts
    }

def generate_time_segment_visualizations(system, segment_results):
    """生成带时间段状态标识的散点图"""

    print("\n🎨 生成时间段可视化图表")
    print("-"*60)

    # 创建输出目录 - 使用绝对路径
    timestamp = datetime.now().strftime("%m-%d-%H-%M-%S")
    base_dir = r"C:\Users\<USER>\Documents\augment-projects\分析在线数据\检测报告"
    output_dir = os.path.join(base_dir, timestamp)
    os.makedirs(output_dir, exist_ok=True)

    print(f"📁 创建输出目录: {output_dir}")

    chart_results = {}
    total_charts = 0

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    for month in segment_results.keys():
        print(f"\n生成{month}月图表...")
        month_charts = {}

        for site_id, site_result in segment_results[month].items():
            # 获取站点信息
            site_data = system.monthly_data[month][system.monthly_data[month]['site_id'] == site_id]
            if len(site_data) == 0:
                continue

            company_name = site_data.iloc[0]['company_name']
            site_name = site_data.iloc[0]['site_name']

            # 生成三合一综合图表（参考07-31-16-13-17格式）
            chart_path = create_time_segment_comprehensive_chart(
                site_result, site_data, company_name, site_name, month, output_dir
            )

            if chart_path:
                month_charts[site_id] = {
                    'chart_path': chart_path,
                    'company_name': company_name,
                    'site_name': site_name,
                    'segments_count': site_result['total_segments']
                }
                total_charts += 1

        chart_results[month] = month_charts
        print(f"  完成{len(month_charts)}个站点的图表生成")

    print(f"\n📊 图表生成完成，共生成{total_charts}个散点图")
    print(f"📁 输出目录: {output_dir}")

    return {
        'output_dir': output_dir,
        'charts': chart_results,
        'total_charts': total_charts
    }

def create_time_segment_scatter_plot(site_result, company_name, site_name, month, output_dir):
    """创建带时间段状态标识的散点图"""

    try:
        segments = site_result['segments']
        if not segments:
            return None

        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 10))

        # 收集所有数据点
        all_timestamps = []
        all_flow_values = []
        all_colors = []
        all_markers = []
        all_sizes = []

        # 绿色虚线位置（时间段分割点）
        segment_boundaries = []

        # 处理每个时间段
        for i, segment in enumerate(segments):
            segment_data = segment['data']
            timestamps = pd.to_datetime(segment_data['timestamp'])
            flow_values = segment_data['flow_value'].values
            anomalies = segment['anomalies']

            # 添加时间段边界
            if i > 0:  # 不添加第一个时间段的开始边界
                segment_boundaries.append(timestamps.iloc[0])

            # 为每个数据点分配颜色和标记
            for j, (ts, val) in enumerate(zip(timestamps, flow_values)):
                all_timestamps.append(ts)
                all_flow_values.append(val)

                # 确定颜色和标记
                if j in anomalies['negative_anomalies']:
                    # 黄色叉号：负值异常
                    all_colors.append('yellow')
                    all_markers.append('x')
                    all_sizes.append(100)
                elif j in anomalies['anomaly_indices']:
                    # 判断是否为明显异常（高评分）
                    score = anomalies['anomaly_scores'].get(j, 0)
                    if score >= 6:  # 高评分异常
                        all_colors.append('red')
                        all_markers.append('o')
                        all_sizes.append(60)
                    else:
                        all_colors.append('yellow')
                        all_markers.append('o')
                        all_sizes.append(50)
                elif val == 0:
                    # 绿色圆点：零值
                    all_colors.append('green')
                    all_markers.append('o')
                    all_sizes.append(40)
                else:
                    # 蓝色圆点：正常值
                    all_colors.append('blue')
                    all_markers.append('o')
                    all_sizes.append(40)

        # 绘制数据点（按颜色分组以优化性能）
        color_groups = {'blue': [], 'yellow': [], 'red': [], 'green': []}
        marker_groups = {'blue': [], 'yellow': [], 'red': [], 'green': []}
        size_groups = {'blue': [], 'yellow': [], 'red': [], 'green': []}

        for i, (color, marker, size) in enumerate(zip(all_colors, all_markers, all_sizes)):
            color_groups[color].append(i)
            marker_groups[color].append(marker)
            size_groups[color].append(size)

        # 分别绘制每种颜色的点
        for color in ['blue', 'green', 'yellow', 'red']:
            if color_groups[color]:
                indices = color_groups[color]
                x_vals = [all_timestamps[i] for i in indices]
                y_vals = [all_flow_values[i] for i in indices]
                markers = [all_markers[i] for i in indices]
                sizes = [all_sizes[i] for i in indices]

                # 分别处理圆点和叉号
                circle_indices = [i for i, m in enumerate(markers) if m == 'o']
                x_indices = [i for i, m in enumerate(markers) if m == 'x']

                if circle_indices:
                    ax.scatter([x_vals[i] for i in circle_indices],
                             [y_vals[i] for i in circle_indices],
                             c=color, marker='o',
                             s=[sizes[i] for i in circle_indices],
                             alpha=0.7, edgecolors='black', linewidth=0.5)

                if x_indices:
                    ax.scatter([x_vals[i] for i in x_indices],
                             [y_vals[i] for i in x_indices],
                             c=color, marker='x',
                             s=[sizes[i] for i in x_indices],
                             alpha=0.8, linewidth=2)

        # 绘制绿色虚线（时间段分割线）
        for boundary in segment_boundaries:
            ax.axvline(x=boundary, color='green', linestyle='--', alpha=0.6, linewidth=2)

        # 添加时间段状态标识
        y_max = max(all_flow_values) if all_flow_values else 1
        y_text_pos = y_max * 0.9

        for i, segment in enumerate(segments):
            start_time = pd.to_datetime(segment['start_time'])
            end_time = pd.to_datetime(segment['end_time'])
            mid_time = start_time + (end_time - start_time) / 2

            status_text = segment['status']['status']
            confidence = segment['status']['confidence']

            # 在时间段中间添加状态标识
            ax.text(mid_time, y_text_pos - (i % 3) * y_max * 0.1,
                   f"段{i+1}: {status_text}\n(置信度:{confidence:.2f})",
                   ha='center', va='top', fontsize=10,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

        # 设置图表属性
        ax.set_xlabel('时间', fontsize=14)
        ax.set_ylabel('流量值', fontsize=14)
        ax.set_title(f'{company_name}-{site_name}-{month}月\n(时间段分析)', fontsize=16, fontweight='bold')

        # 格式化x轴
        ax.xaxis.set_major_formatter(DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 添加网格
        ax.grid(True, alpha=0.3)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        safe_filename = f"{company_name}_{site_name}_{month}月_时间段分析.png"
        safe_filename = "".join(c for c in safe_filename if c.isalnum() or c in '._-')
        chart_path = os.path.join(output_dir, safe_filename)

        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    except Exception as e:
        print(f"生成散点图时出错: {e}")
        return None

def create_time_segment_comprehensive_chart(site_result, site_data, company_name, site_name, month, output_dir):
    """创建时间段分析的三合一综合图表（全面优化版本）"""

    try:
        segments = site_result['segments']
        if not segments:
            return None

        # 按时间戳排序所有数据
        site_month_data_sorted = site_data.sort_values('timestamp').reset_index(drop=True)

        # 创建三个子图的综合图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 12))

        # 导入时间处理模块
        import matplotlib.dates as mdates
        from matplotlib.dates import DateFormatter

        # 提取流量值序列
        flow_values = site_month_data_sorted['flow_value'].values
        timestamps = site_month_data_sorted['timestamp'].values

        # 计算滑动窗口变异系数（用于间断点检测）
        window_size = max(24, len(flow_values) // 20)  # 最小24点窗口
        cv_values = []
        cv_timestamps = []

        for i in range(window_size, len(flow_values)):
            window_data = flow_values[i-window_size:i]
            # 过滤掉零值和负值
            valid_data = window_data[window_data > 0]

            if len(valid_data) > 3:
                mean_val = np.mean(valid_data)
                std_val = np.std(valid_data)
                cv = std_val / mean_val if mean_val > 0 else 10.0
                cv_values.append(min(cv, 10.0))  # 限制最大值为10
            else:
                cv_values.append(10.0)  # 数据不足时设为最大值

            cv_timestamps.append(timestamps[i])

        # 检测间断点（基于变异系数差分的三重条件）
        filtered_breakpoints = []
        if len(cv_values) > 1:
            diff_values = np.diff(cv_values)
            diff_timestamps = cv_timestamps[1:]

            if len(diff_values) > 0:
                diff_std = np.std(diff_values)
                threshold_1 = diff_std * 0.8  # 差分阈值
                threshold_2 = diff_std * 0.5  # 额外阈值
                fixed_threshold = 0.5  # 固定阈值

                # 找出满足三重条件的所有候选间断点
                candidate_breakpoints = []
                for i, diff_val in enumerate(diff_values):
                    if (abs(diff_val) > threshold_1 and
                        abs(diff_val) > threshold_2 and
                        abs(diff_val) >= fixed_threshold):
                        candidate_breakpoints.append((diff_timestamps[i], abs(diff_val)))

                # 按差分值绝对值从大到小排序
                candidate_breakpoints.sort(key=lambda x: x[1], reverse=True)

                # 实施距离过滤和数量限制
                for bp_time, bp_val in candidate_breakpoints:
                    # 检查与已选择间断点的距离
                    is_valid = True
                    for selected_time in filtered_breakpoints:
                        # 计算时间差（小时）
                        time_diff = pd.Timestamp(bp_time) - pd.Timestamp(selected_time)
                        time_diff_hours = abs(time_diff.total_seconds() / 3600)

                        if time_diff_hours < 12:  # 12小时最小间隔
                            is_valid = False
                            break

                    if is_valid:
                        filtered_breakpoints.append(bp_time)
                        if len(filtered_breakpoints) >= 7:  # 最多7个间断点
                            break

        # 收集所有时间段的异常信息
        all_anomaly_indices = []
        all_negative_indices = []
        all_significant_indices = []

        for segment in segments:
            segment_data = segment['data']
            anomalies = segment['anomalies']

            # 将段内索引转换为全局索引
            segment_start_idx = site_month_data_sorted[
                site_month_data_sorted['timestamp'] == pd.to_datetime(segment_data['timestamp'].iloc[0])
            ].index[0] if len(segment_data) > 0 else 0

            for local_idx in anomalies.get('anomaly_indices', []):
                global_idx = segment_start_idx + local_idx
                if global_idx < len(site_month_data_sorted):
                    all_anomaly_indices.append(global_idx)

            for local_idx in anomalies.get('negative_anomalies', []):
                global_idx = segment_start_idx + local_idx
                if global_idx < len(site_month_data_sorted):
                    all_negative_indices.append(global_idx)

            # 高评分异常作为明显异常
            for local_idx, score in anomalies.get('anomaly_scores', {}).items():
                if score >= 6:  # 高评分异常
                    global_idx = segment_start_idx + local_idx
                    if global_idx < len(site_month_data_sorted):
                        all_significant_indices.append(global_idx)

        # === 子图1: 流量值散点图（五色标记系统）===
        ax1.set_title(f'{company_name}-{site_name}-{month}月 流量值', fontsize=12, fontweight='bold')

        # 分离数据
        zero_data = site_month_data_sorted[site_month_data_sorted['flow_value'] == 0]
        positive_data = site_month_data_sorted[site_month_data_sorted['flow_value'] > 0]
        negative_data = site_month_data_sorted[site_month_data_sorted['flow_value'] < 0]

        # 绘制正常值（蓝色）- 确保图例完整性
        normal_indices = set(range(len(site_month_data_sorted))) - set(all_anomaly_indices) - set(all_negative_indices)
        normal_data = site_month_data_sorted.iloc[list(normal_indices)]
        normal_positive = normal_data[normal_data['flow_value'] > 0]

        # 强制显示所有图例项（即使数据为空）
        if len(normal_positive) > 0:
            ax1.scatter(normal_positive['timestamp'], normal_positive['flow_value'],
                       c='blue', alpha=0.6, s=15, label='正常值')
        else:
            # 即使没有数据也添加图例
            ax1.scatter([], [], c='blue', alpha=0.6, s=15, label='正常值')

        # 绘制零值（绿色）
        if len(zero_data) > 0:
            ax1.scatter(zero_data['timestamp'], zero_data['flow_value'],
                       c='green', alpha=0.7, s=20, label='零值', marker='o')
        else:
            ax1.scatter([], [], c='green', alpha=0.7, s=20, label='零值', marker='o')

        # 绘制负值异常（黑色叉号）
        if len(negative_data) > 0:
            ax1.scatter(negative_data['timestamp'], negative_data['flow_value'],
                       c='black', alpha=0.9, s=25, label='负值异常', marker='x', linewidth=1.5)
        else:
            ax1.scatter([], [], c='black', alpha=0.9, s=25, label='负值异常', marker='x', linewidth=1.5)

        # 绘制统计异常值（黄色圆点）
        statistical_anomalies = set(all_anomaly_indices) - set(all_significant_indices) - set(all_negative_indices)
        if statistical_anomalies:
            stat_data = site_month_data_sorted.iloc[list(statistical_anomalies)]
            stat_positive = stat_data[stat_data['flow_value'] >= 0]
            if len(stat_positive) > 0:
                ax1.scatter(stat_positive['timestamp'], stat_positive['flow_value'],
                           c='yellow', alpha=0.8, s=30, label='统计异常',
                           marker='o', edgecolors='orange', linewidth=1.2)
            else:
                ax1.scatter([], [], c='yellow', alpha=0.8, s=30, label='统计异常',
                           marker='o', edgecolors='orange', linewidth=1.2)
        else:
            ax1.scatter([], [], c='yellow', alpha=0.8, s=30, label='统计异常',
                       marker='o', edgecolors='orange', linewidth=1.2)

        # 绘制明显异常值（红色圆点）
        if all_significant_indices:
            sig_data = site_month_data_sorted.iloc[all_significant_indices]
            sig_positive = sig_data[sig_data['flow_value'] >= 0]
            if len(sig_positive) > 0:
                ax1.scatter(sig_positive['timestamp'], sig_positive['flow_value'],
                           c='red', alpha=0.9, s=40, label='明显异常值',
                           marker='o', edgecolors='darkred', linewidth=1)
            else:
                ax1.scatter([], [], c='red', alpha=0.9, s=40, label='明显异常值',
                           marker='o', edgecolors='darkred', linewidth=1)
        else:
            ax1.scatter([], [], c='red', alpha=0.9, s=40, label='明显异常值',
                       marker='o', edgecolors='darkred', linewidth=1)

        # 绘制间断点分割线（绿色虚线）- 基于差分值检测的真正间断点
        if len(filtered_breakpoints) > 0:
            for i, bp_time in enumerate(filtered_breakpoints):
                if i == 0:  # 只在第一条线上添加图例
                    ax1.axvline(x=bp_time, color='green', linestyle='--', alpha=0.7, linewidth=1.5, label='间断点')
                else:
                    ax1.axvline(x=bp_time, color='green', linestyle='--', alpha=0.7, linewidth=1.5)
        else:
            # 即使没有间断点也添加图例（使用空的scatter来添加图例）
            ax1.scatter([], [], color='green', linestyle='--', alpha=0.7, linewidth=1.5, label='间断点')

        ax1.set_xlabel('时间', fontsize=10)
        ax1.set_ylabel('流量值', fontsize=10)
        ax1.legend(fontsize=9)
        ax1.grid(True, alpha=0.3)

        # 自动调整纵坐标刻度和范围
        if len(site_month_data_sorted) > 0:
            y_min = site_month_data_sorted['flow_value'].min()
            y_max = site_month_data_sorted['flow_value'].max()
            y_range = y_max - y_min
            if y_range > 0:
                ax1.set_ylim(y_min - y_range * 0.05, y_max + y_range * 0.15)
            else:
                ax1.set_ylim(y_min - 1, y_max + 1)

        # === 子图2: 变异系数曲线图 ===
        # 确定运行模式
        pattern_type = "时间段分析"
        if segments:
            # 统计各状态的数量
            status_counts = {}
            for segment in segments:
                status = segment['status']['status']
                status_counts[status] = status_counts.get(status, 0) + 1

            # 选择最多的状态作为主要模式
            if status_counts:
                pattern_type = max(status_counts, key=status_counts.get)

        ax2.set_title(f'变异系数 (运行模式: {pattern_type})', fontsize=12, fontweight='bold')

        # 绘制变异系数曲线（已在前面计算）
        if cv_values:
            ax2.plot(cv_timestamps, cv_values, color='blue', linewidth=1.5, alpha=0.8, label='变异系数')
        else:
            # 即使没有数据也添加图例
            ax2.plot([], [], color='blue', linewidth=1.5, alpha=0.8, label='变异系数')

        # 添加阈值线（确保图例完整）
        ax2.axhline(y=0.3, color='red', linestyle='--', alpha=0.7, label='稳定阈值(0.3)')
        ax2.axhline(y=0.7, color='orange', linestyle='--', alpha=0.7, label='停运阈值(0.7)')

        ax2.set_xlabel('时间', fontsize=10)
        ax2.set_ylabel('变异系数', fontsize=10)
        ax2.legend(fontsize=9)
        ax2.grid(True, alpha=0.3)

        # 自动调整变异系数图的纵坐标范围
        if cv_values:
            cv_min = min(cv_values)
            cv_max = max(cv_values)
            cv_range = cv_max - cv_min
            if cv_range > 0:
                ax2.set_ylim(max(0, cv_min - cv_range * 0.1), min(10, cv_max + cv_range * 0.1))
            else:
                ax2.set_ylim(0, max(2, cv_max + 1))
        else:
            ax2.set_ylim(0, 2)

        # === 子图3: 差分值曲线图 ===
        ax3.set_title('差分值（变异系数一阶差分）', fontsize=12, fontweight='bold')

        # 计算变异系数的一阶差分
        if len(cv_values) > 1:
            diff_values = np.diff(cv_values)
            diff_timestamps = cv_timestamps[1:]  # 差分后时间戳对应

            # 绘制差分值曲线
            ax3.plot(diff_timestamps, diff_values, color='green', linewidth=1.5, alpha=0.8, label='差分值')

            # 计算阈值线
            if len(diff_values) > 0:
                diff_std = np.std(diff_values)
                threshold_1 = diff_std * 0.8  # 差分阈值
                threshold_2 = diff_std * 0.5  # 额外阈值
                fixed_threshold = 0.5  # 固定阈值

                ax3.axhline(y=threshold_1, color='red', linestyle='--', alpha=0.7, label=f'差分阈值({threshold_1:.2f})')
                ax3.axhline(y=-threshold_1, color='red', linestyle='--', alpha=0.7)
                ax3.axhline(y=fixed_threshold, color='orange', linestyle=':', alpha=0.7, label='固定阈值(0.5)')
                ax3.axhline(y=-fixed_threshold, color='orange', linestyle=':', alpha=0.7)

                # 标记所有满足三重条件的候选间断点
                all_breakpoints = []
                for i, diff_val in enumerate(diff_values):
                    if (abs(diff_val) > threshold_1 and
                        abs(diff_val) > threshold_2 and
                        abs(diff_val) >= fixed_threshold):
                        all_breakpoints.append((diff_timestamps[i], diff_val))

                # 绘制所有候选间断点
                if all_breakpoints:
                    bp_times, bp_values = zip(*all_breakpoints)
                    ax3.scatter(bp_times, bp_values, color='red', s=50, alpha=0.9,
                               label='间断点', marker='o', edgecolors='darkred', linewidth=2)
                else:
                    # 即使没有间断点也添加图例
                    ax3.scatter([], [], color='red', s=50, alpha=0.9,
                               label='间断点', marker='o', edgecolors='darkred', linewidth=2)
            else:
                # 没有差分值时也要添加完整图例
                ax3.axhline(y=0.4, color='red', linestyle='--', alpha=0.7, label='差分阈值(0.40)')
                ax3.axhline(y=0.5, color='orange', linestyle=':', alpha=0.7, label='固定阈值(0.5)')
                ax3.scatter([], [], color='red', s=50, alpha=0.9,
                           label='间断点', marker='o', edgecolors='darkred', linewidth=2)
        else:
            # 没有变异系数数据时也要添加完整图例
            ax3.plot([], [], color='green', linewidth=1.5, alpha=0.8, label='差分值')
            ax3.axhline(y=0.4, color='red', linestyle='--', alpha=0.7, label='差分阈值(0.40)')
            ax3.axhline(y=0.5, color='orange', linestyle=':', alpha=0.7, label='固定阈值(0.5)')
            ax3.scatter([], [], color='red', s=50, alpha=0.9,
                       label='间断点', marker='o', edgecolors='darkred', linewidth=2)

        ax3.set_xlabel('时间', fontsize=10)
        ax3.set_ylabel('差分值', fontsize=10)
        ax3.legend(fontsize=9)
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=0.5)

        # 自动调整差分值图的纵坐标范围
        if len(cv_values) > 1:
            diff_values = np.diff(cv_values)
            if len(diff_values) > 0:
                diff_min = min(diff_values)
                diff_max = max(diff_values)
                diff_range = diff_max - diff_min
                if diff_range > 0:
                    ax3.set_ylim(diff_min - diff_range * 0.1, diff_max + diff_range * 0.1)
                else:
                    ax3.set_ylim(diff_min - 1, diff_max + 1)

        # 设置所有子图的时间轴格式（固定两天一个刻度）
        for ax in [ax1, ax2, ax3]:
            if len(site_month_data_sorted) > 0:
                ax.xaxis.set_major_formatter(DateFormatter('%m-%d %H:%M'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))  # 固定两天一个刻度
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 添加整体标题和信息
        fig.suptitle(f'{company_name}-{site_name}-{month}月 三合一综合图表', fontsize=14, fontweight='bold')

        # 添加时间范围信息和技术参数
        if len(site_month_data_sorted) > 0:
            start_time = site_month_data_sorted['timestamp'].min()
            end_time = site_month_data_sorted['timestamp'].max()
            breakpoint_count = len(filtered_breakpoints)
            segment_count = breakpoint_count + 1
            fig.text(0.02, 0.02,
                    f'时间范围: {start_time.strftime("%Y-%m-%d %H:%M")} 至 {end_time.strftime("%Y-%m-%d %H:%M")}\n'
                    f'滑动窗口: {window_size}点 | 距离过滤: 12点 | 运行模式: {pattern_type} | 间断点: {breakpoint_count}个 | 时间段: {segment_count}个',
                    fontsize=8, alpha=0.7)

        # 保存图表
        safe_company = company_name.replace('/', '_').replace('\\\\', '_')[:15]
        safe_site = site_name.replace('/', '_').replace('\\\\', '_')[:20]
        chart_filename = f"{safe_company}_{safe_site}_{month}月_三合一综合图表_距离过滤12点.png"
        chart_path = os.path.join(output_dir, chart_filename)

        plt.tight_layout()
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    except Exception as e:
        print(f"生成三合一综合图表时出错: {e}")
        return None

def generate_time_segment_report(segment_results, chart_results):
    """生成时间段分析报告"""

    print("\n📋 生成时间段分析报告")
    print("-"*60)

    output_dir = chart_results['output_dir']

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 报告输出目录: {output_dir}")

    # 生成方法说明文档
    method_doc_path = os.path.join(output_dir, "时间段异常检测方法说明.txt")
    generate_method_documentation(method_doc_path)

    # 生成Excel详细报告 - 使用固定时间戳避免不一致
    timestamp_str = os.path.basename(output_dir)  # 使用目录名作为时间戳
    excel_path = os.path.join(output_dir, f"时间段异常检测详细报告_{timestamp_str}.xlsx")
    generate_excel_report(segment_results, chart_results, excel_path)

    # 统计总体结果
    total_sites = 0
    total_segments = 0
    status_stats = {'停运状态': 0, '单状态运行': 0, '正常波动': 0}

    for month_results in segment_results.values():
        for site_result in month_results.values():
            total_sites += 1
            total_segments += site_result['total_segments']

            for segment in site_result['segments']:
                status = segment['status']['status']
                status_stats[status] = status_stats.get(status, 0) + 1

    print(f"\n📊 时间段分析统计:")
    print(f"  处理站点数: {total_sites}")
    print(f"  总时间段数: {total_segments}")
    print(f"  状态分布:")
    for status, count in status_stats.items():
        percentage = count / total_segments * 100 if total_segments > 0 else 0
        print(f"    {status}: {count}个 ({percentage:.1f}%)")

    print(f"\n📁 报告文件:")
    print(f"  方法说明: {method_doc_path}")
    print(f"  详细报告: {excel_path}")
    print(f"  散点图: {chart_results['total_charts']}个")

def generate_method_documentation(file_path):
    """生成方法说明文档"""

    content = f"""时间段异常检测方法说明 V6.0 (简化版)
===========================================

## 系统概述
本系统实现基于时间段的简化运行状态识别和异常检测，将复杂的多状态识别简化为3种基本状态，
并完全停用机器学习方法，仅使用统计方法进行异常检测。

## 1. 时间段划分方法
### 分割点检测算法
- 使用滑动窗口（24小时）检测变异系数变化
- 三重条件检测：变异系数差异 > 0.8，均值差异 > 0.5，中位数差异 > 0.5
- 最小间隔：12小时（避免过度分割）
- 最大时间段数：8个（7个分割点）

### 时间段标识
- 在散点图中用绿色虚线标识时间段分割点
- 每个时间段添加状态标识和置信度信息

## 2. 简化运行状态识别（3种状态）
### 停运状态
- 判定条件：零值或极低值（≤0.2）比例 ≥ 70%
- 数据特征：长期稳定在零值或极低值范围
- 置信度计算：0.5 + 零值比例（最高0.95）

### 单状态运行
- 判定条件：零值比例 < 70% 且 变异系数 < 0.3
- 数据特征：围绕某个中心值相对稳定波动
- 置信度计算：0.6 + (0.3 - 变异系数)（最高0.95）

### 正常波动
- 判定条件：零值比例 < 70% 且 变异系数 ≥ 0.3
- 数据特征：存在明显的规律性或随机性变化
- 置信度计算：0.5 + min(变异系数, 1.0)（最高0.95）

## 3. 简化异常检测方法
### 停用的方法
- ❌ LOF局部异常因子检测（完全停用）
- ❌ DBSCAN聚类检测（完全停用）
- ❌ 机器学习层检测（完全停用）

### 保留的统计方法（等权重）
#### P5/P95百分位数阈值法
- 计算正值数据的5%和95%分位数
- 异常判定：值 < P5 或 值 > P95
- 可信度评分：3分

#### IQR四分位距法
- 计算：Q1-1.5×IQR 和 Q3+1.5×IQR
- 异常判定：值在IQR范围外
- 可信度评分：3分

#### MAD中位数绝对偏差法
- 计算：中位数 ± 2.5×MAD
- 异常判定：值在MAD范围外
- 可信度评分：3分

### 业务规则层
- 负值异常：所有负值自动判定为异常
- 可信度评分：5分（最高优先级）

## 4. 区间保护机制
### 保护区间计算
- 基准值：该时间段内数据的中位数
- 调整系数：根据标准差反向调整
  * 标准差 < 0.1：调整系数 = 1.5（扩大保护区间）
  * 标准差 0.1-0.5：调整系数 = 1.0（正常保护区间）
  * 标准差 > 0.5：调整系数 = 0.5（缩小保护区间）
- 保护区间 = 中位数 ± (标准差 × 调整系数)

### 保护规则
- 落在保护区间内的正值不判定为异常
- 保护区间不包含负值（负值仍按业务规则判定为异常）
- 仅适用于正值数据的保护

## 5. 四色标记系统
- 🔵 蓝色圆点：正常值（通过所有检测）
- 🟡 黄色圆点：统计异常值（被统计方法检测出）
- 🟡 黄色叉号：负值异常（违反物理规律）
- 🔴 红色圆点：明显异常值（综合评分≥6分的高置信度异常）
- 🟢 绿色圆点：零值（特殊标记）

## 6. 系统优势
- 简化运行状态识别，提高识别准确性
- 停用复杂机器学习方法，降低误报率
- 基于时间段的独立分析，更符合实际运行模式
- 区间保护机制减少正常波动的误判
- 等权重统计方法，避免方法偏向性

## 7. 技术参数
- 时间段检测窗口：24小时
- 变异系数阈值：0.8
- 固定阈值：0.5
- 最小时间段间隔：12小时
- 最大时间段数：8个
- 停运状态阈值：70%零值比例
- 单状态运行阈值：变异系数<0.3
- 保护区间调整系数：0.5-1.5

生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
系统版本：V6.0 简化版
"""

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def generate_excel_report(segment_results, chart_results, excel_path):
    """生成Excel详细报告"""

    # 这里可以添加Excel报告生成逻辑
    # 由于篇幅限制，暂时创建一个简单的占位符
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 创建汇总表
        summary_data = []
        for month, month_results in segment_results.items():
            for site_id, site_result in month_results.items():
                for segment in site_result['segments']:
                    summary_data.append({
                        '月份': month,
                        '站点ID': site_id,
                        '时间段': segment['segment_id'],
                        '开始时间': segment['start_time'],
                        '结束时间': segment['end_time'],
                        '数据点数': segment['data_count'],
                        '运行状态': segment['status']['status'],
                        '置信度': segment['status']['confidence'],
                        '异常数量': segment['anomalies']['total_anomalies'],
                        '检测方法': ','.join(segment['anomalies']['methods_used'])
                    })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='时间段分析汇总', index=False)

def main():
    """主函数"""
    try:
        print("开始V6.0基于时间段的简化异常检测系统...")

        results = implement_v6_time_segment_optimization()

        print(f"\n🎉 V6.0时间段简化系统实施完成！")
        print(f"✅ 时间段划分完成")
        print(f"✅ 简化运行状态识别完成")
        print(f"✅ 统计方法异常检测完成")
        print(f"✅ 区间保护机制实施完成")
        print(f"✅ 时间段可视化完成")
        print(f"✅ 简化报告生成完成")

        # 统计结果
        total_sites = 0
        total_segments = 0
        for month_results in results['segment_results'].values():
            for site_result in month_results.values():
                total_sites += 1
                total_segments += site_result['total_segments']

        print(f"\n📊 处理统计:")
        print(f"   处理站点数: {total_sites}个")
        print(f"   分析时间段数: {total_segments}个")
        print(f"   生成图表数: {results['charts']['total_charts']}个")
        print(f"   输出位置: {results['charts']['output_dir']}")

    except Exception as e:
        print(f"\n❌ 系统实施过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
