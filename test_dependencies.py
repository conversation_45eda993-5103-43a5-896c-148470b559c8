#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试依赖包是否正常
"""

print("开始检查Python依赖包...")

try:
    import pandas as pd
    print("✓ pandas 导入成功")
except ImportError as e:
    print(f"✗ pandas 导入失败: {e}")

try:
    import numpy as np
    print("✓ numpy 导入成功")
except ImportError as e:
    print(f"✗ numpy 导入失败: {e}")

try:
    import matplotlib.pyplot as plt
    print("✓ matplotlib 导入成功")
except ImportError as e:
    print(f"✗ matplotlib 导入失败: {e}")

try:
    from sklearn.cluster import DBSCAN
    print("✓ sklearn 导入成功")
except ImportError as e:
    print(f"✗ sklearn 导入失败: {e}")

try:
    import openpyxl
    print("✓ openpyxl 导入成功")
except ImportError as e:
    print(f"✗ openpyxl 导入失败: {e}")

print("\n检查数据文件...")
import os
data_file = "数据读取/唐山2025-05.xlsx"
if os.path.exists(data_file):
    print(f"✓ 数据文件存在: {data_file}")
    try:
        df = pd.read_excel(data_file)
        print(f"✓ 数据文件读取成功，共 {len(df)} 行数据")
        print(f"✓ 数据列名: {list(df.columns)}")
    except Exception as e:
        print(f"✗ 数据文件读取失败: {e}")
else:
    print(f"✗ 数据文件不存在: {data_file}")

print("\n依赖检查完成！")
