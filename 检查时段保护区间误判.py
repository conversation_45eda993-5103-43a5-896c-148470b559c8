#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查时段保护区间误判分析
分析大唐国际发电股份有限公司陡河热_8号脱销B侧入口的时段4是否存在误判
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

def analyze_protection_interval_misjudgment():
    """分析保护区间可能的误判情况"""
    
    print("🔍 分析时段保护区间误判情况")
    print("="*60)
    
    # 加载数据
    data_file = "数据读取/唐山2025-05.xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    print(f"📁 加载数据文件: {data_file}")
    df = pd.read_excel(data_file)
    
    # 数据预处理
    df = df.dropna(subset=['流量'])
    df['timestamp'] = pd.to_datetime(df['数据时间'])
    df['flow_value'] = df['流量']
    
    # 筛选目标企业和监测点
    target_company = "大唐国际发电股份有限公司陡河热电厂"
    target_site = "8号脱销B侧入口"
    
    # 尝试不同的匹配方式
    possible_companies = df['企业名称'].unique()
    possible_sites = df['监测点名称'].unique()
    
    print(f"🎯 目标企业: {target_company}")
    print(f"🎯 目标监测点: {target_site}")
    print()
    
    # 模糊匹配企业名称
    matched_company = None
    for company in possible_companies:
        if "大唐" in company and "陡河" in company:
            matched_company = company
            break
    
    if not matched_company:
        print("⚠️  未找到匹配的企业，显示所有包含'大唐'的企业:")
        for company in possible_companies:
            if "大唐" in company:
                print(f"   - {company}")
        return
    
    # 模糊匹配监测点
    matched_site = None
    company_data = df[df['企业名称'] == matched_company]
    company_sites = company_data['监测点名称'].unique()
    
    for site in company_sites:
        if "8号" in site and ("脱销" in site or "脱硝" in site) and "B" in site:
            matched_site = site
            break
    
    if not matched_site:
        print(f"⚠️  未找到匹配的监测点，显示企业 {matched_company} 的所有监测点:")
        for site in company_sites:
            print(f"   - {site}")
        return
    
    print(f"✅ 匹配到企业: {matched_company}")
    print(f"✅ 匹配到监测点: {matched_site}")
    print()
    
    # 提取目标数据
    target_data = df[(df['企业名称'] == matched_company) & 
                     (df['监测点名称'] == matched_site)].copy()
    
    if len(target_data) == 0:
        print("❌ 未找到目标数据")
        return
    
    target_data = target_data.sort_values('timestamp').reset_index(drop=True)
    print(f"📊 目标数据记录数: {len(target_data)}")
    print(f"📅 时间范围: {target_data['timestamp'].min()} 至 {target_data['timestamp'].max()}")
    print()
    
    # 模拟时间段分割（简化版本）
    flow_values = target_data['flow_value'].values
    timestamps = target_data['timestamp'].values
    
    # 使用变异系数检测分割点
    window_size = 24
    cv_values = []
    
    for i in range(window_size, len(flow_values)):
        window_data = flow_values[i-window_size:i]
        valid_data = window_data[window_data > 0]
        
        if len(valid_data) > 3:
            mean_val = np.mean(valid_data)
            std_val = np.std(valid_data)
            cv = std_val / mean_val if mean_val > 0 else 15.0
            cv_values.append(min(cv, 15.0))
        else:
            cv_values.append(15.0)
    
    # 检测间断点
    if len(cv_values) > 1:
        diff_values = np.diff(cv_values)
        threshold = np.std(diff_values) * 0.8
        
        breakpoints = []
        for i, diff_val in enumerate(diff_values):
            if abs(diff_val) > max(threshold, 0.5):
                bp_time = timestamps[i + window_size]
                breakpoints.append(bp_time)
        
        # 距离过滤
        filtered_breakpoints = []
        for bp_time in sorted(breakpoints):
            is_valid = True
            for selected_time in filtered_breakpoints:
                time_diff = pd.Timestamp(bp_time) - pd.Timestamp(selected_time)
                time_diff_hours = abs(time_diff.total_seconds() / 3600)
                if time_diff_hours < 12:
                    is_valid = False
                    break
            if is_valid:
                filtered_breakpoints.append(bp_time)
                if len(filtered_breakpoints) >= 7:
                    break
    else:
        filtered_breakpoints = []
    
    print(f"🔄 检测到 {len(filtered_breakpoints)} 个时间段分割点")
    
    # 创建时间段
    segment_boundaries = [timestamps[0]]
    for bp_time in filtered_breakpoints:
        time_diffs = [abs((pd.Timestamp(t) - pd.Timestamp(bp_time)).total_seconds()) for t in timestamps]
        closest_idx = np.argmin(time_diffs)
        if closest_idx < len(timestamps) - 1:
            segment_boundaries.append(timestamps[closest_idx])
    segment_boundaries.append(timestamps[-1])
    segment_boundaries = sorted(list(set(segment_boundaries)))
    
    print(f"📊 总共分为 {len(segment_boundaries)-1} 个时间段")
    print()
    
    # 分析每个时间段，重点关注时段4
    for i in range(len(segment_boundaries) - 1):
        start_time = segment_boundaries[i]
        end_time = segment_boundaries[i + 1]
        
        # 获取时间段数据
        segment_mask = (target_data['timestamp'] >= start_time) & (target_data['timestamp'] <= end_time)
        segment_data = target_data[segment_mask].copy()
        
        if len(segment_data) == 0:
            continue
        
        segment_id = i + 1
        print(f"📈 时间段 {segment_id} 分析:")
        print(f"   时间范围: {start_time} 至 {end_time}")
        print(f"   数据点数: {len(segment_data)}")
        
        # 计算保护区间
        flow_data = segment_data['flow_value']
        positive_data = flow_data[flow_data > 0]
        
        if len(positive_data) >= 3:
            median_val = np.median(positive_data)
            std_val = np.std(positive_data)
            
            # 调整系数
            if std_val < 0.1:
                adjustment_factor = 1.5
                protection_level = "扩大保护"
            elif std_val < 0.5:
                adjustment_factor = 1.0
                protection_level = "正常保护"
            else:
                adjustment_factor = 0.5
                protection_level = "缩小保护"
            
            protection_range = std_val * adjustment_factor
            lower_bound = max(0, median_val - protection_range)
            upper_bound = median_val + protection_range
            
            print(f"   数据统计: 中位数={median_val:.3f}, 标准差={std_val:.3f}")
            print(f"   保护区间: [{lower_bound:.3f}, {upper_bound:.3f}] ({protection_level})")
            
            # 统计方法异常检测
            statistical_anomalies = []
            
            # P5/P95方法
            if len(positive_data) >= 5:
                p5 = np.percentile(positive_data, 5)
                p95 = np.percentile(positive_data, 95)
                p5_p95_anomalies = flow_data[(flow_data < p5) | (flow_data > p95)].index.tolist()
                statistical_anomalies.extend(p5_p95_anomalies)
                print(f"   P5/P95阈值: [{p5:.3f}, {p95:.3f}], 检出异常: {len(p5_p95_anomalies)}个")
            
            # IQR方法
            if len(positive_data) >= 4:
                q1 = np.percentile(positive_data, 25)
                q3 = np.percentile(positive_data, 75)
                iqr = q3 - q1
                iqr_lower = q1 - 1.5 * iqr
                iqr_upper = q3 + 1.5 * iqr
                iqr_anomalies = flow_data[(flow_data < iqr_lower) | (flow_data > iqr_upper)].index.tolist()
                statistical_anomalies.extend(iqr_anomalies)
                print(f"   IQR阈值: [{iqr_lower:.3f}, {iqr_upper:.3f}], 检出异常: {len(iqr_anomalies)}个")
            
            # MAD方法
            mad = np.median(np.abs(positive_data - median_val))
            mad_lower = median_val - 2.5 * mad
            mad_upper = median_val + 2.5 * mad
            mad_anomalies = flow_data[(flow_data < mad_lower) | (flow_data > mad_upper)].index.tolist()
            statistical_anomalies.extend(mad_anomalies)
            print(f"   MAD阈值: [{mad_lower:.3f}, {mad_upper:.3f}], 检出异常: {len(mad_anomalies)}个")
            
            # 去重统计异常
            statistical_anomalies = list(set(statistical_anomalies))
            print(f"   统计方法总计检出: {len(statistical_anomalies)}个异常候选")
            
            # 应用保护区间
            protected_count = 0
            final_anomalies = []
            
            for idx in statistical_anomalies:
                val = flow_data.iloc[idx - segment_data.index[0]]  # 调整索引
                if val > 0 and lower_bound <= val <= upper_bound:
                    protected_count += 1
                    print(f"     保护: 值{val:.3f}在保护区间内")
                else:
                    final_anomalies.append(idx)
                    print(f"     异常: 值{val:.3f}超出保护区间")
            
            print(f"   保护区间保护了: {protected_count}个值")
            print(f"   最终异常数量: {len(final_anomalies)}个")
            
            # 特别关注时段4
            if segment_id == 4:
                print(f"\n🎯 时段4详细分析:")
                print(f"   这是目标分析时段")
                
                # 分析可能的误判情况
                if protected_count > 0:
                    print(f"   ✅ 保护区间发挥了作用，保护了{protected_count}个可能的误判")
                
                if len(final_anomalies) > 0:
                    print(f"   ⚠️  仍有{len(final_anomalies)}个异常，需要人工验证是否为真异常")
                    
                    # 显示异常值详情
                    for idx in final_anomalies[:5]:  # 最多显示5个
                        val = flow_data.iloc[idx - segment_data.index[0]]
                        print(f"     异常值: {val:.3f}")
                
                # 判断保护区间设置是否合理
                protection_ratio = protection_range / median_val if median_val > 0 else 0
                print(f"   保护区间相对大小: {protection_ratio:.1%}")
                
                if protection_ratio < 0.1:
                    print(f"   💡 保护区间可能过小，可能存在误判")
                elif protection_ratio > 0.5:
                    print(f"   💡 保护区间可能过大，可能漏检异常")
                else:
                    print(f"   ✅ 保护区间大小合理")
        
        else:
            print(f"   ⚠️  正值数据不足，无法计算保护区间")
        
        print()
    
    print(f"🎯 结论:")
    print(f"   需要结合图表中时段4的具体数据点分布来判断:")
    print(f"   1. 查看被标记为异常的点是否明显偏离正常范围")
    print(f"   2. 查看保护区间是否覆盖了正常波动范围")
    print(f"   3. 查看异常点是否符合业务逻辑")
    print(f"   4. 对比其他时段的异常检测结果")

if __name__ == "__main__":
    analyze_protection_interval_misjudgment()
    print(f"\n📝 分析完成！")
    print(f"请结合图表中的实际标记情况进行综合判断。")
