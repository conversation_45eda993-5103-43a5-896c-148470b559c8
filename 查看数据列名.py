#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看数据文件的列名
"""

import pandas as pd
import os

def check_data_columns():
    """检查数据文件的列名"""
    
    data_file = "数据读取/唐山2025-05.xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    print(f"📁 加载数据文件: {data_file}")
    df = pd.read_excel(data_file)
    
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名:")
    for i, col in enumerate(df.columns):
        print(f"   {i+1}. {col}")
    
    print(f"\n📈 前5行数据:")
    print(df.head())

if __name__ == "__main__":
    check_data_columns()
